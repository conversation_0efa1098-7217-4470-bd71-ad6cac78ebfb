/**
 * n8nboy Integration System
 * Seamlessly integrates all node modification systems with the existing n8nboy extension
 */

(function() {
    'use strict';

    // Integration configuration
    const INTEGRATION_CONFIG = {
        autoStart: true,
        enableLogging: true,
        features: {
            universalModifier: true,
            editorButtons: true,
            realtimeMonitoring: true,
            bulkOperations: true
        },
        compatibility: {
            n8nVersions: ['0.200.0', '0.210.0', '0.220.0', '1.0.0'],
            browserSupport: ['chrome', 'firefox', 'edge', 'safari']
        }
    };

    // Main Integration Class
    class N8nBoyIntegration {
        constructor() {
            this.initialized = false;
            this.systems = new Map();
            this.eventBus = new EventTarget();
            this.stats = {
                startTime: Date.now(),
                nodesProcessed: 0,
                operationsExecuted: 0,
                errorsEncountered: 0
            };
        }

        // Initialize the integration system
        async initialize() {
            if (this.initialized) return;

            console.log('🚀 Initializing n8nboy Integration System v2.4');
            
            try {
                await this.waitForDependencies();
                await this.initializeSystems();
                await this.setupIntegrations();
                await this.setupEventHandlers();
                await this.performHealthCheck();
                
                this.initialized = true;
                this.logSuccess('n8nboy Integration System fully initialized');
                
                // Trigger initialization complete event
                this.eventBus.dispatchEvent(new CustomEvent('integrationReady', {
                    detail: { timestamp: Date.now(), systems: Array.from(this.systems.keys()) }
                }));
                
            } catch (error) {
                this.logError('Failed to initialize n8nboy Integration System', error);
                throw error;
            }
        }

        // Wait for all dependencies to be available
        async waitForDependencies() {
            const dependencies = [
                'UniversalNodeModifier',
                'EditorButtonInjector', 
                'RealtimeMonitor',
                'BulkNodeOperations'
            ];

            const maxWait = 10000; // 10 seconds
            const checkInterval = 100; // 100ms
            let waited = 0;

            while (waited < maxWait) {
                const allAvailable = dependencies.every(dep => window[dep]);
                
                if (allAvailable) {
                    this.log('All dependencies available');
                    return;
                }

                await this.delay(checkInterval);
                waited += checkInterval;
            }

            throw new Error('Timeout waiting for dependencies');
        }

        // Initialize all systems
        async initializeSystems() {
            this.log('Initializing core systems...');

            // Initialize Universal Node Modifier
            if (INTEGRATION_CONFIG.features.universalModifier && window.UniversalNodeModifier) {
                await window.UniversalNodeModifier.initialize();
                this.systems.set('universalModifier', window.UniversalNodeModifier);
                this.log('✅ Universal Node Modifier initialized');
            }

            // Initialize Editor Button Injector
            if (INTEGRATION_CONFIG.features.editorButtons && window.EditorButtonInjector) {
                this.systems.set('editorButtons', window.EditorButtonInjector);
                this.log('✅ Editor Button Injector initialized');
            }

            // Initialize Real-time Monitor
            if (INTEGRATION_CONFIG.features.realtimeMonitoring && window.RealtimeMonitor) {
                window.RealtimeMonitor.startMonitoring();
                this.systems.set('realtimeMonitor', window.RealtimeMonitor);
                this.log('✅ Real-time Monitor initialized');
            }

            // Initialize Bulk Operations
            if (INTEGRATION_CONFIG.features.bulkOperations && window.BulkNodeOperations) {
                this.systems.set('bulkOperations', window.BulkNodeOperations);
                this.log('✅ Bulk Operations initialized');
            }
        }

        // Setup cross-system integrations
        async setupIntegrations() {
            this.log('Setting up system integrations...');

            // Integrate editor button injection with node monitoring
            if (this.systems.has('realtimeMonitor') && this.systems.has('editorButtons')) {
                const monitor = this.systems.get('realtimeMonitor');
                const buttonInjector = this.systems.get('editorButtons');

                monitor.addEventListener('editorOpened', (event) => {
                    const { editor, editorData } = event.detail;
                    buttonInjector.injectButtons(editor, editorData.nodeType, 'auto');
                });

                this.log('🔗 Editor monitoring integrated with button injection');
            }

            // Integrate bulk operations with node classification
            if (this.systems.has('universalModifier') && this.systems.has('bulkOperations')) {
                const modifier = this.systems.get('universalModifier');
                const bulkOps = this.systems.get('bulkOperations');

                // Add enhanced filtering based on node classification
                bulkOps.filters.set('byComplexity', (nodes, complexity) => {
                    return nodes.filter(node => {
                        const classification = modifier.classifyNode(node.element);
                        return classification.classification?.complexity === complexity;
                    });
                });

                this.log('🔗 Bulk operations integrated with node classification');
            }

            // Setup automatic node enhancement
            if (this.systems.has('realtimeMonitor') && this.systems.has('universalModifier')) {
                const monitor = this.systems.get('realtimeMonitor');
                const modifier = this.systems.get('universalModifier');

                monitor.addEventListener('nodeAdded', (event) => {
                    const { node } = event.detail;
                    setTimeout(() => {
                        modifier.processNode(node);
                        this.stats.nodesProcessed++;
                    }, 50);
                });

                this.log('🔗 Automatic node enhancement enabled');
            }
        }

        // Setup global event handlers
        async setupEventHandlers() {
            this.log('Setting up global event handlers...');

            // Listen for n8n workflow changes
            document.addEventListener('n8n-workflow-changed', () => {
                this.handleWorkflowChange();
            });

            // Listen for performance alerts
            if (this.systems.has('realtimeMonitor')) {
                const monitor = this.systems.get('realtimeMonitor');
                monitor.addEventListener('performanceAlert', (event) => {
                    this.handlePerformanceAlert(event.detail);
                });
            }

            // Setup keyboard shortcuts
            document.addEventListener('keydown', (event) => {
                this.handleKeyboardShortcuts(event);
            });

            // Setup context menu integration
            document.addEventListener('contextmenu', (event) => {
                this.handleContextMenu(event);
            });

            this.log('🎯 Global event handlers setup complete');
        }

        // Perform system health check
        async performHealthCheck() {
            this.log('Performing system health check...');

            const healthReport = {
                systems: {},
                overall: 'healthy',
                issues: []
            };

            // Check each system
            for (const [name, system] of this.systems) {
                try {
                    const status = this.checkSystemHealth(system);
                    healthReport.systems[name] = status;
                    
                    if (status.status !== 'healthy') {
                        healthReport.issues.push(`${name}: ${status.message}`);
                    }
                } catch (error) {
                    healthReport.systems[name] = { status: 'error', message: error.message };
                    healthReport.issues.push(`${name}: ${error.message}`);
                }
            }

            // Determine overall health
            if (healthReport.issues.length > 0) {
                healthReport.overall = healthReport.issues.length > 2 ? 'critical' : 'warning';
            }

            this.log(`Health check complete: ${healthReport.overall}`, healthReport);
            return healthReport;
        }

        // Check individual system health
        checkSystemHealth(system) {
            if (!system) {
                return { status: 'error', message: 'System not available' };
            }

            // Check if system has required methods
            const requiredMethods = ['cleanup'];
            const missingMethods = requiredMethods.filter(method => typeof system[method] !== 'function');
            
            if (missingMethods.length > 0) {
                return { 
                    status: 'warning', 
                    message: `Missing methods: ${missingMethods.join(', ')}` 
                };
            }

            // Check system-specific health
            if (system.getStats && typeof system.getStats === 'function') {
                const stats = system.getStats();
                if (stats.initialized === false) {
                    return { status: 'warning', message: 'System not fully initialized' };
                }
            }

            return { status: 'healthy', message: 'All checks passed' };
        }

        // Event handlers
        handleWorkflowChange() {
            this.log('Workflow changed - refreshing node modifications');
            
            if (this.systems.has('universalModifier')) {
                const modifier = this.systems.get('universalModifier');
                setTimeout(() => {
                    modifier.modifyExistingNodes();
                }, 500);
            }
        }

        handlePerformanceAlert(alert) {
            this.logWarning(`Performance alert: ${alert.type} = ${alert.value} (threshold: ${alert.threshold})`);
            
            // Take corrective action if needed
            if (alert.type === 'memoryUsage' && alert.value > alert.threshold * 1.5) {
                this.performEmergencyCleanup();
            }
        }

        handleKeyboardShortcuts(event) {
            // Ctrl+Shift+N: Apply highlight to all nodes
            if (event.ctrlKey && event.shiftKey && event.key === 'N') {
                event.preventDefault();
                this.highlightAllNodes();
            }

            // Ctrl+Shift+R: Reset all node modifications
            if (event.ctrlKey && event.shiftKey && event.key === 'R') {
                event.preventDefault();
                this.resetAllNodes();
            }

            // Ctrl+Shift+S: Show system statistics
            if (event.ctrlKey && event.shiftKey && event.key === 'S') {
                event.preventDefault();
                this.showSystemStats();
            }
        }

        handleContextMenu(event) {
            const node = event.target.closest('.vue-flow__node');
            if (node && this.systems.has('bulkOperations')) {
                // Add custom context menu options for nodes
                // This would integrate with the existing context menu system
            }
        }

        // Utility methods
        async highlightAllNodes() {
            if (this.systems.has('bulkOperations')) {
                const bulkOps = this.systems.get('bulkOperations');
                await bulkOps.applyToAllNodes('highlight', { color: '#00ff41', intensity: 0.8 });
                this.log('All nodes highlighted');
            }
        }

        async resetAllNodes() {
            if (this.systems.has('bulkOperations')) {
                const bulkOps = this.systems.get('bulkOperations');
                await bulkOps.applyToAllNodes('reset');
                this.log('All nodes reset');
            }
        }

        showSystemStats() {
            const stats = this.getSystemStats();
            console.table(stats);
            this.log('System statistics displayed in console');
        }

        performEmergencyCleanup() {
            this.logWarning('Performing emergency cleanup...');
            
            this.systems.forEach((system, name) => {
                if (system.cleanup && typeof system.cleanup === 'function') {
                    try {
                        system.cleanup();
                        this.log(`Cleaned up ${name}`);
                    } catch (error) {
                        this.logError(`Failed to cleanup ${name}`, error);
                    }
                }
            });
        }

        // Statistics and monitoring
        getSystemStats() {
            const stats = {
                integration: {
                    initialized: this.initialized,
                    uptime: Date.now() - this.stats.startTime,
                    systemCount: this.systems.size,
                    ...this.stats
                }
            };

            // Get stats from each system
            this.systems.forEach((system, name) => {
                if (system.getStats && typeof system.getStats === 'function') {
                    stats[name] = system.getStats();
                } else if (system.getMonitoringStatus && typeof system.getMonitoringStatus === 'function') {
                    stats[name] = system.getMonitoringStatus();
                } else {
                    stats[name] = { status: 'available' };
                }
            });

            return stats;
        }

        // Logging utilities
        log(message, data = null) {
            if (INTEGRATION_CONFIG.enableLogging) {
                console.log(`[n8nboy] ${message}`, data || '');
            }
        }

        logSuccess(message, data = null) {
            if (INTEGRATION_CONFIG.enableLogging) {
                console.log(`[n8nboy] ✅ ${message}`, data || '');
            }
        }

        logWarning(message, data = null) {
            if (INTEGRATION_CONFIG.enableLogging) {
                console.warn(`[n8nboy] ⚠️ ${message}`, data || '');
            }
        }

        logError(message, error = null) {
            if (INTEGRATION_CONFIG.enableLogging) {
                console.error(`[n8nboy] ❌ ${message}`, error || '');
            }
            this.stats.errorsEncountered++;
        }

        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // Cleanup
        cleanup() {
            this.log('Cleaning up n8nboy Integration System...');
            
            this.systems.forEach((system, name) => {
                if (system.cleanup && typeof system.cleanup === 'function') {
                    system.cleanup();
                }
            });
            
            this.systems.clear();
            this.initialized = false;
            
            this.log('n8nboy Integration System cleaned up');
        }
    }

    // Create global instance
    window.N8nBoyIntegration = new N8nBoyIntegration();

    // Auto-initialize if enabled
    if (INTEGRATION_CONFIG.autoStart) {
        // Wait for DOM to be ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                window.N8nBoyIntegration.initialize();
            });
        } else {
            window.N8nBoyIntegration.initialize();
        }
    }

    // Expose global API
    window.n8nboy = {
        integration: window.N8nBoyIntegration,
        modifier: () => window.UniversalNodeModifier,
        monitor: () => window.RealtimeMonitor,
        bulkOps: () => window.BulkNodeOperations,
        buttons: () => window.EditorButtonInjector,

        // Convenience methods
        highlightAll: () => window.N8nBoyIntegration.highlightAllNodes(),
        resetAll: () => window.N8nBoyIntegration.resetAllNodes(),
        stats: () => window.N8nBoyIntegration.getSystemStats(),
        health: () => window.N8nBoyIntegration.performHealthCheck(),

        // Debug methods
        reprocessAll: () => window.UniversalNodeModifier?.forceReprocessAllNodes(),
        debugNode: (node) => window.UniversalNodeModifier?.debugNode(node),
        debugAllNodes: () => {
            const nodes = document.querySelectorAll('.vue-flow__node');
            console.log(`🐛 Debugging ${nodes.length} nodes:`);
            nodes.forEach((node, i) => {
                console.log(`\n--- Node ${i + 1} ---`);
                window.UniversalNodeModifier?.debugNode(node);
            });
        }
    };

    console.log('📦 n8nboy Integration System loaded');

})();
