{"manifest_version": 3, "name": "n8n boy", "version": "2.4", "description": "AI-powered extension for n8n by <PERSON>", "permissions": ["contextMenus", "activeTab", "storage", "management"], "host_permissions": ["*://*/*", "https://openrouter.ai/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["*://*/*"], "js": ["universal-node-modifier.js", "editor-button-injection.js", "realtime-monitoring.js", "bulk-operations.js", "n8nboy-integration.js", "content.js"]}], "action": {"default_popup": "popup.html", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "web_accessible_resources": [{"resources": ["icons/*.png", "data/custom-structures.json", "hacker-notification.css", "LICENSE.txt", "LICENSE.json", "DMCA-NOTICE.txt", "LEGAL-README.md"], "matches": ["*://*/*"]}]}