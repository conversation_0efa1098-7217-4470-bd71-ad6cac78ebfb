/**
 * Bulk Node Operations System for n8nboy Extension
 * Comprehensive system for applying modifications to multiple nodes simultaneously
 */

(function() {
    'use strict';

    // Bulk operations configuration
    const BULK_CONFIG = {
        batchSize: 10, // Process nodes in batches to avoid performance issues
        delay: 50, // Delay between batch operations (ms)
        maxConcurrent: 5, // Maximum concurrent operations
        operations: {
            visual: ['highlight', 'dim', 'hide', 'show', 'resize'],
            functional: ['enable', 'disable', 'reset', 'configure'],
            data: ['export', 'import', 'backup', 'restore'],
            analysis: ['validate', 'optimize', 'debug', 'profile']
        }
    };

    // Bulk Node Operations System
    class BulkNodeOperations {
        constructor() {
            this.operationQueue = [];
            this.activeOperations = new Map();
            this.operationHistory = [];
            this.filters = new Map();
            this.setupFilters();
        }

        // Setup node filters
        setupFilters() {
            this.filters.set('byType', (nodes, type) => 
                nodes.filter(node => this.getNodeType(node.element) === type)
            );
            
            this.filters.set('byCategory', (nodes, category) => 
                nodes.filter(node => this.getNodeCategory(node.element) === category)
            );
            
            this.filters.set('byStatus', (nodes, status) => 
                nodes.filter(node => this.getNodeStatus(node.element) === status)
            );
            
            this.filters.set('bySelection', (nodes, selected) => 
                nodes.filter(node => node.element.classList.contains('selected') === selected)
            );
            
            this.filters.set('byError', (nodes, hasError) => 
                nodes.filter(node => this.hasNodeError(node.element) === hasError)
            );
            
            this.filters.set('byConnections', (nodes, minConnections) => 
                nodes.filter(node => this.getConnectionCount(node.element) >= minConnections)
            );
            
            this.filters.set('byCustom', (nodes, predicate) => 
                nodes.filter(predicate)
            );
        }

        // Get all nodes with classification
        getAllNodes() {
            const nodeElements = document.querySelectorAll('.vue-flow__node');
            return Array.from(nodeElements).map(element => {
                const classification = window.UniversalNodeModifier?.classifyNode?.(element) || {};
                return {
                    element,
                    id: this.getNodeId(element),
                    type: this.getNodeType(element),
                    category: this.getNodeCategory(element),
                    ...classification
                };
            });
        }

        // Filter nodes by criteria
        filterNodes(criteria) {
            const allNodes = this.getAllNodes();
            let filteredNodes = allNodes;

            Object.entries(criteria).forEach(([filterType, value]) => {
                const filter = this.filters.get(filterType);
                if (filter) {
                    filteredNodes = filter(filteredNodes, value);
                }
            });

            return filteredNodes;
        }

        // Apply operation to all nodes
        async applyToAllNodes(operation, options = {}) {
            const nodes = this.getAllNodes();
            return this.applyToNodes(nodes, operation, options);
        }

        // Apply operation to nodes by type
        async applyToNodesByType(nodeType, operation, options = {}) {
            const nodes = this.filterNodes({ byType: nodeType });
            return this.applyToNodes(nodes, operation, options);
        }

        // Apply operation to nodes by category
        async applyToNodesByCategory(category, operation, options = {}) {
            const nodes = this.filterNodes({ byCategory: category });
            return this.applyToNodes(nodes, operation, options);
        }

        // Apply operation to selected nodes
        async applyToSelectedNodes(operation, options = {}) {
            const nodes = this.filterNodes({ bySelection: true });
            return this.applyToNodes(nodes, operation, options);
        }

        // Apply operation to nodes with errors
        async applyToErrorNodes(operation, options = {}) {
            const nodes = this.filterNodes({ byError: true });
            return this.applyToNodes(nodes, operation, options);
        }

        // Apply operation to filtered nodes
        async applyToFilteredNodes(criteria, operation, options = {}) {
            const nodes = this.filterNodes(criteria);
            return this.applyToNodes(nodes, operation, options);
        }

        // Core method to apply operations to nodes
        async applyToNodes(nodes, operation, options = {}) {
            const operationId = this.generateOperationId();
            const startTime = Date.now();
            
            console.log(`🔄 Starting bulk operation: ${operation} on ${nodes.length} nodes`);
            
            const operationData = {
                id: operationId,
                operation,
                nodeCount: nodes.length,
                startTime,
                status: 'running',
                progress: 0,
                results: [],
                errors: []
            };
            
            this.activeOperations.set(operationId, operationData);
            
            try {
                const results = await this.processBatches(nodes, operation, options, operationData);
                
                operationData.status = 'completed';
                operationData.endTime = Date.now();
                operationData.duration = operationData.endTime - operationData.startTime;
                operationData.results = results;
                
                this.operationHistory.push(operationData);
                this.activeOperations.delete(operationId);
                
                console.log(`✅ Bulk operation completed: ${operation} (${operationData.duration}ms)`);
                return operationData;
                
            } catch (error) {
                operationData.status = 'failed';
                operationData.error = error.message;
                operationData.endTime = Date.now();
                
                this.activeOperations.delete(operationId);
                console.error(`❌ Bulk operation failed: ${operation}`, error);
                throw error;
            }
        }

        // Process nodes in batches
        async processBatches(nodes, operation, options, operationData) {
            const batches = this.createBatches(nodes, BULK_CONFIG.batchSize);
            const results = [];
            
            for (let i = 0; i < batches.length; i++) {
                const batch = batches[i];
                const batchResults = await this.processBatch(batch, operation, options);
                results.push(...batchResults);
                
                // Update progress
                operationData.progress = ((i + 1) / batches.length) * 100;
                
                // Add delay between batches to prevent UI blocking
                if (i < batches.length - 1) {
                    await this.delay(BULK_CONFIG.delay);
                }
            }
            
            return results;
        }

        // Process a single batch
        async processBatch(batch, operation, options) {
            const promises = batch.map(node => this.applyOperationToNode(node, operation, options));
            return Promise.all(promises);
        }

        // Apply operation to individual node
        async applyOperationToNode(node, operation, options) {
            try {
                const result = await this.executeOperation(node, operation, options);
                return {
                    nodeId: node.id,
                    success: true,
                    result
                };
            } catch (error) {
                return {
                    nodeId: node.id,
                    success: false,
                    error: error.message
                };
            }
        }

        // Execute specific operation
        async executeOperation(node, operation, options) {
            switch (operation) {
                case 'highlight':
                    return this.highlightNode(node, options);
                case 'dim':
                    return this.dimNode(node, options);
                case 'hide':
                    return this.hideNode(node, options);
                case 'show':
                    return this.showNode(node, options);
                case 'resize':
                    return this.resizeNode(node, options);
                case 'enable':
                    return this.enableNode(node, options);
                case 'disable':
                    return this.disableNode(node, options);
                case 'reset':
                    return this.resetNode(node, options);
                case 'configure':
                    return this.configureNode(node, options);
                case 'export':
                    return this.exportNode(node, options);
                case 'backup':
                    return this.backupNode(node, options);
                case 'validate':
                    return this.validateNode(node, options);
                case 'optimize':
                    return this.optimizeNode(node, options);
                case 'debug':
                    return this.debugNode(node, options);
                case 'addIcon':
                    return this.addIconToNode(node, options);
                case 'addBorder':
                    return this.addBorderToNode(node, options);
                case 'addTooltip':
                    return this.addTooltipToNode(node, options);
                default:
                    throw new Error(`Unknown operation: ${operation}`);
            }
        }

        // Visual operations
        highlightNode(node, options = {}) {
            const color = options.color || '#ffff00';
            const intensity = options.intensity || 0.7;
            
            node.element.style.boxShadow = `0 0 20px rgba(255, 255, 0, ${intensity})`;
            node.element.style.border = `2px solid ${color}`;
            
            return { highlighted: true, color, intensity };
        }

        dimNode(node, options = {}) {
            const opacity = options.opacity || 0.3;
            
            node.element.style.opacity = opacity;
            node.element.style.filter = 'grayscale(50%)';
            
            return { dimmed: true, opacity };
        }

        hideNode(node, options = {}) {
            const method = options.method || 'visibility'; // 'visibility' or 'display'
            
            if (method === 'display') {
                node.element.style.display = 'none';
            } else {
                node.element.style.visibility = 'hidden';
            }
            
            return { hidden: true, method };
        }

        showNode(node, options = {}) {
            node.element.style.display = '';
            node.element.style.visibility = '';
            node.element.style.opacity = '';
            node.element.style.filter = '';
            
            return { shown: true };
        }

        resizeNode(node, options = {}) {
            const scale = options.scale || 1.2;
            
            node.element.style.transform = `scale(${scale})`;
            node.element.style.transformOrigin = 'center';
            
            return { resized: true, scale };
        }

        // Functional operations
        enableNode(node, options = {}) {
            node.element.classList.remove('disabled');
            node.element.style.pointerEvents = '';
            
            return { enabled: true };
        }

        disableNode(node, options = {}) {
            node.element.classList.add('disabled');
            node.element.style.pointerEvents = 'none';
            node.element.style.opacity = '0.5';
            
            return { disabled: true };
        }

        resetNode(node, options = {}) {
            // Reset all custom styles
            const stylesToReset = [
                'boxShadow', 'border', 'opacity', 'filter', 'display', 
                'visibility', 'transform', 'pointerEvents'
            ];
            
            stylesToReset.forEach(style => {
                node.element.style[style] = '';
            });
            
            // Remove custom classes
            node.element.classList.remove('disabled', 'highlighted', 'dimmed');
            
            return { reset: true };
        }

        configureNode(node, options = {}) {
            // Apply configuration options
            Object.entries(options).forEach(([key, value]) => {
                if (key.startsWith('data-')) {
                    node.element.setAttribute(key, value);
                } else if (key === 'class') {
                    node.element.className = value;
                } else if (key === 'style') {
                    Object.assign(node.element.style, value);
                }
            });
            
            return { configured: true, options };
        }

        // Enhancement operations
        addIconToNode(node, options = {}) {
            const icon = options.icon || '⭐';
            const position = options.position || 'top-right';
            
            if (window.UniversalNodeModifier?.addNodeIcon) {
                window.UniversalNodeModifier.addNodeIcon(node.element, icon, options.color || '#ffff00');
            }
            
            return { iconAdded: true, icon, position };
        }

        addBorderToNode(node, options = {}) {
            const color = options.color || '#00ff41';
            const width = options.width || '2px';
            const style = options.style || 'solid';
            
            node.element.style.border = `${width} ${style} ${color}`;
            
            return { borderAdded: true, color, width, style };
        }

        addTooltipToNode(node, options = {}) {
            const text = options.text || `Node: ${node.type}`;
            
            node.element.title = text;
            
            return { tooltipAdded: true, text };
        }

        // Analysis operations
        validateNode(node, options = {}) {
            const validation = {
                hasType: !!node.type,
                hasId: !!node.id,
                isVisible: node.element.style.display !== 'none',
                hasErrors: this.hasNodeError(node.element),
                isConnected: this.getConnectionCount(node.element) > 0
            };
            
            validation.isValid = validation.hasType && validation.hasId && !validation.hasErrors;
            
            return validation;
        }

        optimizeNode(node, options = {}) {
            // Basic optimization: remove unnecessary attributes and clean up styles
            const optimizations = [];
            
            // Remove empty attributes
            Array.from(node.element.attributes).forEach(attr => {
                if (!attr.value.trim()) {
                    node.element.removeAttribute(attr.name);
                    optimizations.push(`Removed empty attribute: ${attr.name}`);
                }
            });
            
            return { optimized: true, optimizations };
        }

        debugNode(node, options = {}) {
            const debug = {
                id: node.id,
                type: node.type,
                category: node.category,
                element: node.element,
                attributes: Array.from(node.element.attributes).map(attr => ({
                    name: attr.name,
                    value: attr.value
                })),
                styles: window.getComputedStyle(node.element),
                connections: this.getConnectionCount(node.element),
                hasErrors: this.hasNodeError(node.element)
            };
            
            console.log('🐛 Node Debug Info:', debug);
            return debug;
        }

        // Data operations
        exportNode(node, options = {}) {
            const exportData = {
                id: node.id,
                type: node.type,
                category: node.category,
                position: this.getNodePosition(node.element),
                attributes: Array.from(node.element.attributes).map(attr => ({
                    name: attr.name,
                    value: attr.value
                })),
                timestamp: Date.now()
            };
            
            return exportData;
        }

        backupNode(node, options = {}) {
            const backup = this.exportNode(node, options);
            
            // Store backup in localStorage or send to server
            const backupKey = `n8nboy-backup-${node.id}-${Date.now()}`;
            localStorage.setItem(backupKey, JSON.stringify(backup));
            
            return { backed_up: true, backupKey, backup };
        }

        // Utility methods
        createBatches(items, batchSize) {
            const batches = [];
            for (let i = 0; i < items.length; i += batchSize) {
                batches.push(items.slice(i, i + batchSize));
            }
            return batches;
        }

        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        generateOperationId() {
            return `op-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        }

        // Node utility methods
        getNodeId(element) {
            return element.getAttribute('data-id') || element.id || 'unknown';
        }

        getNodeType(element) {
            const typeElement = element.querySelector('[data-node-type]');
            return typeElement ? typeElement.getAttribute('data-node-type') : 'unknown';
        }

        getNodeCategory(element) {
            const nodeType = this.getNodeType(element);
            if (nodeType.includes('trigger')) return 'trigger';
            if (nodeType.includes('langchain') || nodeType.includes('ai')) return 'ai';
            if (nodeType.includes('http')) return 'communication';
            if (nodeType.includes('database')) return 'data';
            if (nodeType.includes('code')) return 'logic';
            return 'utility';
        }

        getNodeStatus(element) {
            if (element.classList.contains('error')) return 'error';
            if (element.classList.contains('success')) return 'success';
            if (element.classList.contains('running')) return 'running';
            return 'idle';
        }

        hasNodeError(element) {
            return element.classList.contains('error') || 
                   element.querySelector('.error') !== null;
        }

        getConnectionCount(element) {
            return element.querySelectorAll('.vue-flow__handle').length;
        }

        getNodePosition(element) {
            const rect = element.getBoundingClientRect();
            return { x: rect.left, y: rect.top, width: rect.width, height: rect.height };
        }

        // Status and statistics
        getOperationStatus() {
            return {
                activeOperations: this.activeOperations.size,
                queuedOperations: this.operationQueue.length,
                completedOperations: this.operationHistory.length,
                activeDetails: Array.from(this.activeOperations.values())
            };
        }

        getOperationHistory() {
            return this.operationHistory.slice(-50); // Return last 50 operations
        }

        // Cleanup
        cleanup() {
            this.operationQueue = [];
            this.activeOperations.clear();
            this.operationHistory = [];
            console.log('🧹 Bulk Operations System cleaned up');
        }
    }

    // Create global instance
    window.BulkNodeOperations = new BulkNodeOperations();

    // Integrate with Universal Node Modifier
    if (window.UniversalNodeModifier) {
        window.UniversalNodeModifier.bulkOps = window.BulkNodeOperations;
        
        // Add bulk operation methods to the main modifier
        window.UniversalNodeModifier.applyToAllNodes = function(operation, options) {
            return this.bulkOps.applyToAllNodes(operation, options);
        };
        
        window.UniversalNodeModifier.applyToNodesByType = function(type, operation, options) {
            return this.bulkOps.applyToNodesByType(type, operation, options);
        };
        
        window.UniversalNodeModifier.applyToSelectedNodes = function(operation, options) {
            return this.bulkOps.applyToSelectedNodes(operation, options);
        };
        
        window.UniversalNodeModifier.filterNodes = function(criteria) {
            return this.bulkOps.filterNodes(criteria);
        };
        
        console.log('🔄 Bulk Operations System integrated');
    }

    console.log('📦 Bulk Operations System loaded');

})();
