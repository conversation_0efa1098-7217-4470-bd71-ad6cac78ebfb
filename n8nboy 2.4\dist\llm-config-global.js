const _0xcca559=_0x1722;function _0x1722(_0xfbd8c6,_0x48a2e8){const _0x1fd49f=_0x1fd4();return _0x1722=function(_0x172259,_0x94aa8){_0x172259=_0x172259-0x184;let _0x31d9c8=_0x1fd49f[_0x172259];return _0x31d9c8;},_0x1722(_0xfbd8c6,_0x48a2e8);}function _0x1fd4(){const _0x379099=['OpenAI\x20GPT-4.1\x20Mini','452625wncvEa','40539343zTgVzR','12uoJpcj','defaultModel','192003NYgeSr','4983TunaUd','9YykZto','keys','Advanced\x20conversational\x20model\x20with\x20strong\x20performance','DeepSeek','Anthropic','9893103nowkBO','18ttloUM','n8n\x20Boy\x20Extension','23720wJNVRi','getModelNames','openai/gpt-4.1-mini','96gBzLNk','getDefaultModel','x-ai/grok-3-beta','988qCxNfb','getModelConfig','LLM_CONFIG','mistralai/mistral-large-2407','getInternalModelConfig','Advanced\x20multimodal\x20model\x20with\x20strong\x20reasoning','getModelConfigById','models','OpenAI','15295090sMwgSK','values','2471GDgtwI'];_0x1fd4=function(){return _0x379099;};return _0x1fd4();}(function(_0x1a24ed,_0x31d593){const _0x47fea5=_0x1722,_0x60412c=_0x1a24ed();while(!![]){try{const _0x6c4a2d=-parseInt(_0x47fea5(0x193))/0x1*(parseInt(_0x47fea5(0x19b))/0x2)+parseInt(_0x47fea5(0x194))/0x3*(-parseInt(_0x47fea5(0x1a3))/0x4)+parseInt(_0x47fea5(0x18f))/0x5*(-parseInt(_0x47fea5(0x1a0))/0x6)+-parseInt(_0x47fea5(0x18d))/0x7*(parseInt(_0x47fea5(0x19d))/0x8)+-parseInt(_0x47fea5(0x195))/0x9*(-parseInt(_0x47fea5(0x18b))/0xa)+-parseInt(_0x47fea5(0x19a))/0xb*(-parseInt(_0x47fea5(0x191))/0xc)+parseInt(_0x47fea5(0x190))/0xd;if(_0x6c4a2d===_0x31d593)break;else _0x60412c['push'](_0x60412c['shift']());}catch(_0x32b719){_0x60412c['push'](_0x60412c['shift']());}}}(_0x1fd4,0xdf222),window[_0xcca559(0x184)]={'apiEndpoint':'https://openrouter.ai/api/v1/chat/completions','keyPrefix':'sk-or-v1-','keyMinLength':0x1e,'defaultModel':'anthropic/claude-sonnet-4','models':{'openai/gpt-4.1':{'id':'openai/gpt-4.1','name':'OpenAI\x20GPT-4.1','provider':_0xcca559(0x18a),'description':'Latest\x20GPT-4\x20model\x20with\x20enhanced\x20capabilities','contextLength':0x1f400,'supportsImages':!![],'supportsTools':!![]},'anthropic/claude-sonnet-4':{'id':'anthropic/claude-sonnet-4','name':'Claude\x20Sonnet\x204','provider':_0xcca559(0x199),'description':'Most\x20capable\x20model\x20for\x20complex\x20reasoning\x20and\x20analysis','contextLength':0xf4240,'supportsImages':!![],'supportsTools':!![]},'google/gemini-2.5-pro-preview':{'id':'google/gemini-2.5-pro-preview','name':'Gemini\x202.5\x20Pro','provider':'Google','description':_0xcca559(0x187),'contextLength':0xf4240,'supportsImages':!![],'supportsTools':!![]},'deepseek/deepseek-chat-v3-0324':{'id':'deepseek/deepseek-chat-v3-0324','name':'DeepSeek\x20Chat\x20V3','provider':_0xcca559(0x198),'description':_0xcca559(0x197),'contextLength':0x1f400,'supportsImages':!![],'supportsTools':!![]},'x-ai/grok-3-beta':{'id':_0xcca559(0x1a2),'name':'Grok\x203\x20Beta','provider':'xAI','description':'Advanced\x20reasoning\x20model\x20with\x20real-time\x20knowledge','contextLength':0x1f400,'supportsImages':!![],'supportsTools':!![]},'mistralai/mistral-large-2407':{'id':_0xcca559(0x185),'name':'Mistral\x20Large','provider':'Mistral','description':'High-performance\x20model\x20for\x20complex\x20reasoning\x20tasks','contextLength':0x1f400,'supportsImages':!![],'supportsTools':!![]}},'temperatures':{'codeGeneration':0.2,'promptOptimization':0.3,'promptGeneration':0.7,'curlGeneration':0.3,'jsonGeneration':0.5,'jsonFixing':0.3,'n8nHelper':0.4,'agentRouting':0.1,'workflowAgent':0.4,'troubleshootingAgent':0.3,'integrationAgent':0.4,'generalAgent':0.5,'imageAnalysisAgent':0.2,'default':0.7},'maxTokens':0xbb8,'optionalHeaders':{'httpReferer':undefined,'xTitle':_0xcca559(0x19c)}});const INTERNAL_MODELS={'openai/gpt-4.1-mini':{'id':_0xcca559(0x19f),'name':_0xcca559(0x18e),'provider':_0xcca559(0x18a),'description':'Fast\x20and\x20efficient\x20model\x20optimized\x20for\x20routing\x20and\x20classification','contextLength':0x1f400,'supportsImages':!![],'supportsTools':!![]}};window[_0xcca559(0x1a4)]=function(_0x2e4244){const _0x353171=_0xcca559;return window[_0x353171(0x184)][_0x353171(0x189)][_0x2e4244]||null;},window[_0xcca559(0x188)]=function(_0x45bdfd){return Object['values'](window['LLM_CONFIG']['models'])['find'](_0x179a58=>_0x179a58['id']===_0x45bdfd)||null;},window['getTemperature']=function(_0x1fd8a5){const _0x189400=_0xcca559;return window[_0x189400(0x184)]['temperatures'][_0x1fd8a5]||window['LLM_CONFIG']['temperatures']['default'];},window['validateApiKey']=function(_0x5d1f57){const _0x8a3dac=_0xcca559;return!!(_0x5d1f57&&_0x5d1f57['startsWith'](window[_0x8a3dac(0x184)]['keyPrefix'])&&_0x5d1f57['length']>=window['LLM_CONFIG']['keyMinLength']);},window[_0xcca559(0x19e)]=function(){const _0x53dff9=_0xcca559;return Object['keys'](window[_0x53dff9(0x184)]['models']);},window['getModelIds']=function(){const _0x439071=_0xcca559;return Object[_0x439071(0x18c)](window['LLM_CONFIG'][_0x439071(0x189)])['map'](_0x2514e0=>_0x2514e0['id']);},window[_0xcca559(0x1a1)]=function(){const _0x4b59ea=_0xcca559,_0x5657f5=Object[_0x4b59ea(0x196)](window['LLM_CONFIG']['models'])['find'](_0x49c28c=>window['LLM_CONFIG']['models'][_0x49c28c]['id']===window['LLM_CONFIG'][_0x4b59ea(0x192)]);return window['LLM_CONFIG']['models'][_0x5657f5||'anthropic/claude-sonnet-4'];},window[_0xcca559(0x186)]=function(_0x721c45){return INTERNAL_MODELS[_0x721c45]||null;};