function _0x13ee(_0x29391c,_0x1a9b71){const _0x12bbcc=_0x12bb();return _0x13ee=function(_0x13ee98,_0x4a01db){_0x13ee98=_0x13ee98-0xb2;let _0x13bad4=_0x12bbcc[_0x13ee98];return _0x13bad4;},_0x13ee(_0x29391c,_0x1a9b71);}function _0x12bb(){const _0xea0b98=['69IbKjzq','6gHiUzF','3615664lgBleF','DeepSeek\x20Chat\x20V3','startsWith','map','Claude\x20Sonnet\x204','11LVXbBV','11412tGggKi','OpenAI','Google','176yeryzK','n8n\x20Boy\x20Extension','temperatures','Advanced\x20conversational\x20model\x20with\x20strong\x20performance','Anthropic','157424IeGiNr','Advanced\x20multimodal\x20model\x20with\x20strong\x20reasoning','25751170VWkddu','x-ai/grok-3-beta','keys','models','sk-or-v1-','Mistral\x20Large','4237562laGuFq','getModelConfigById','values','LLM_CONFIG','getModelNames','anthropic/claude-sonnet-4','1830155kNKgpQ','getTemperature','31518jKuDgC'];_0x12bb=function(){return _0xea0b98;};return _0x12bb();}(function(_0x3382c6,_0x14cb62){const _0x1db8c3=_0x13ee,_0x408a7d=_0x3382c6();while(!![]){try{const _0x49c14a=-parseInt(_0x1db8c3(0xc1))/0x1+-parseInt(_0x1db8c3(0xb9))/0x2*(parseInt(_0x1db8c3(0xd2))/0x3)+-parseInt(_0x1db8c3(0xb3))/0x4+parseInt(_0x1db8c3(0xcf))/0x5*(-parseInt(_0x1db8c3(0xb2))/0x6)+-parseInt(_0x1db8c3(0xc9))/0x7+-parseInt(_0x1db8c3(0xbc))/0x8*(-parseInt(_0x1db8c3(0xd1))/0x9)+parseInt(_0x1db8c3(0xc3))/0xa*(parseInt(_0x1db8c3(0xb8))/0xb);if(_0x49c14a===_0x14cb62)break;else _0x408a7d['push'](_0x408a7d['shift']());}catch(_0xd055a2){_0x408a7d['push'](_0x408a7d['shift']());}}}(_0x12bb,0x772fa),!(function(){'use strict';const _0xd24c9c=_0x13ee;const LLM_CONFIG={'apiEndpoint':'https://openrouter.ai/api/v1/chat/completions','keyPrefix':_0xd24c9c(0xc7),'keyMinLength':0x1e,'defaultModel':'anthropic/claude-sonnet-4','models':{'openai/gpt-4.1':{'id':'openai/gpt-4.1','name':'OpenAI\x20GPT-4.1','provider':_0xd24c9c(0xba),'description':'Latest\x20GPT-4\x20model\x20with\x20enhanced\x20capabilities','contextLength':0x1f400,'supportsImages':!0x0,'supportsTools':!0x0},'anthropic/claude-sonnet-4':{'id':'anthropic/claude-sonnet-4','name':_0xd24c9c(0xb7),'provider':_0xd24c9c(0xc0),'description':'Most\x20capable\x20model\x20for\x20complex\x20reasoning\x20and\x20analysis','contextLength':0xf4240,'supportsImages':!0x0,'supportsTools':!0x0},'google/gemini-2.5-pro-preview':{'id':'google/gemini-2.5-pro-preview','name':'Gemini\x202.5\x20Pro','provider':_0xd24c9c(0xbb),'description':_0xd24c9c(0xc2),'contextLength':0xf4240,'supportsImages':!0x0,'supportsTools':!0x0},'deepseek/deepseek-chat-v3-0324':{'id':'deepseek/deepseek-chat-v3-0324','name':_0xd24c9c(0xb4),'provider':'DeepSeek','description':_0xd24c9c(0xbf),'contextLength':0x1f400,'supportsImages':!0x1,'supportsTools':!0x0},'deepseek/deepseek-r1-0528:free':{'id':'deepseek/deepseek-r1-0528:free','name':'DeepSeek\x20R1','provider':'DeepSeek','description':'Advanced\x20reasoning\x20model\x20with\x20enhanced\x20capabilities','contextLength':0x1f400,'supportsImages':!0x1,'supportsTools':!0x0},'x-ai/grok-3-beta':{'id':_0xd24c9c(0xc4),'name':'Grok\x203\x20Beta','provider':'xAI','description':'Advanced\x20reasoning\x20model\x20with\x20real-time\x20knowledge','contextLength':0x1f400,'supportsImages':!0x0,'supportsTools':!0x0},'mistralai/mistral-large-2407':{'id':'mistralai/mistral-large-2407','name':_0xd24c9c(0xc8),'provider':'Mistral','description':'High-performance\x20model\x20for\x20complex\x20reasoning\x20tasks','contextLength':0x1f400,'supportsImages':!0x1,'supportsTools':!0x0}},'temperatures':{'codeGeneration':0.2,'promptOptimization':0.3,'promptGeneration':0.7,'curlGeneration':0.3,'jsonGeneration':0.5,'jsonFixing':0.3,'n8nHelper':0.4,'agentRouting':0.1,'workflowAgent':0.4,'troubleshootingAgent':0.3,'integrationAgent':0.4,'generalAgent':0.5,'imageAnalysisAgent':0.2,'default':0.7},'maxTokens':0xbb8,'optionalHeaders':{'httpReferer':void 0x0,'xTitle':_0xd24c9c(0xbd)}};function getModelConfig(_0x2f6156){return LLM_CONFIG['models'][_0x2f6156]||null;}function getModelConfigById(_0x1817f3){const _0x40334e=_0xd24c9c;return Object[_0x40334e(0xcb)](LLM_CONFIG['models'])['find'](_0x3b5502=>_0x3b5502['id']===_0x1817f3)||null;}function getTemperature(_0x57942f){const _0x7ae5a5=_0xd24c9c;return LLM_CONFIG[_0x7ae5a5(0xbe)][_0x57942f]||LLM_CONFIG[_0x7ae5a5(0xbe)]['default'];}function validateApiKey(_0x5b497d){const _0x57d405=_0xd24c9c;return!!(_0x5b497d&&_0x5b497d[_0x57d405(0xb5)](LLM_CONFIG['keyPrefix'])&&_0x5b497d['length']>=LLM_CONFIG['keyMinLength']);}function getModelNames(){const _0x5917a6=_0xd24c9c;return Object[_0x5917a6(0xc5)](LLM_CONFIG['models']);}function getModelIds(){const _0x53ad34=_0xd24c9c;return Object['values'](LLM_CONFIG[_0x53ad34(0xc6)])[_0x53ad34(0xb6)](_0x666514=>_0x666514['id']);}function getDefaultModel(){const _0x4d57da=_0xd24c9c,_0x3a61c2=Object['keys'](LLM_CONFIG['models'])['find'](_0x5b7f7c=>LLM_CONFIG[_0x4d57da(0xc6)][_0x5b7f7c]['id']===LLM_CONFIG['defaultModel']);return LLM_CONFIG['models'][_0x3a61c2||_0x4d57da(0xce)];}window[_0xd24c9c(0xcc)]=LLM_CONFIG,window['getModelConfig']=getModelConfig,window[_0xd24c9c(0xca)]=getModelConfigById,window[_0xd24c9c(0xd0)]=getTemperature,window['validateApiKey']=validateApiKey,window[_0xd24c9c(0xcd)]=getModelNames,window['getModelIds']=getModelIds,window['getDefaultModel']=getDefaultModel;}()));