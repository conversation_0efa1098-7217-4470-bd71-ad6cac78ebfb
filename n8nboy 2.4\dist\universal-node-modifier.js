/**
 * Universal Node Modifier System for n8nboy Extension
 * Comprehensive system to detect, monitor, and modify all n8n nodes
 */

(function() {
    'use strict';

    // Core configuration
    const CONFIG = {
        selectors: {
            nodes: '.vue-flow__node',
            nodeType: '[data-node-type]',
            canvas: '.vue-flow',
            editor: '.ndv-wrapper',
            codeEditor: '.code-node-editor',
            jsonEditor: '.cm-content[data-language="json"]',
            systemMessage: '[data-test-id="parameter-input-systemMessage"]'
        },
        nodeTypes: {
            CODE: 'n8n-nodes-base.code',
            AI_AGENT: '@n8n/n8n-nodes-langchain.agent',
            HTTP: 'n8n-nodes-base.httpRequest',
            SET: 'n8n-nodes-base.set',
            PARSER: '@n8n/n8n-nodes-langchain.outputParserStructured'
        },
        styles: {
            codeNode: '0 0 10px rgba(255, 255, 0, 0.5)',
            aiAgent: '0 0 15px rgba(0, 170, 255, 0.7)',
            httpNode: '0 0 10px rgba(0, 255, 0, 0.5)',
            setNode: '0 0 10px rgba(255, 0, 255, 0.5)',
            parserNode: '0 0 10px rgba(255, 165, 0, 0.5)'
        }
    };

    // Universal Node Modifier Class
    class UniversalNodeModifier {
        constructor() {
            this.initialized = false;
            this.observers = new Map();
            this.modifiedNodes = new Set();
            this.nodeRegistry = new Map();
            this.editorObserver = null;
        }

        // Initialize the system
        initialize() {
            if (this.initialized) return;
            
            console.log('🚀 Initializing Universal Node Modifier System');
            
            // Wait for DOM and n8n to be ready
            this.waitForN8n().then(() => {
                this.setupNodeMonitoring();
                this.setupEditorMonitoring();
                this.modifyExistingNodes();
                this.setupEventListeners();
                this.initialized = true;
                console.log('✅ Universal Node Modifier System initialized');
            });
        }

        // Wait for n8n interface to be ready
        async waitForN8n() {
            return new Promise((resolve) => {
                const checkN8n = () => {
                    if (this.isN8nPage() && document.querySelector(CONFIG.selectors.canvas)) {
                        resolve();
                    } else {
                        setTimeout(checkN8n, 500);
                    }
                };
                checkN8n();
            });
        }

        // Check if we're on an n8n page
        isN8nPage() {
            return window.location.hostname.includes('n8n') || 
                   document.querySelector(CONFIG.selectors.canvas) !== null ||
                   document.querySelector('.n8n-app') !== null;
        }

        // Setup monitoring for new nodes
        setupNodeMonitoring() {
            const canvas = document.querySelector(CONFIG.selectors.canvas);
            if (!canvas) return;

            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === 1 && node.matches && node.matches(CONFIG.selectors.nodes)) {
                            setTimeout(() => this.processNode(node), 100);
                        }
                    });
                });
            });

            observer.observe(canvas, {
                childList: true,
                subtree: true
            });

            this.observers.set('nodeMonitoring', observer);
            console.log('📡 Node monitoring setup complete');
        }

        // Setup monitoring for editor changes
        setupEditorMonitoring() {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (node.nodeType === 1) {
                            this.checkForEditors(node);
                        }
                    });
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            this.observers.set('editorMonitoring', observer);
            console.log('📝 Editor monitoring setup complete');
        }

        // Check for editor elements and inject buttons
        checkForEditors(element) {
            // Check for code editors
            const codeEditors = element.querySelectorAll ? 
                element.querySelectorAll(CONFIG.selectors.codeEditor) : [];
            codeEditors.forEach(editor => this.injectCodeButtons(editor));

            // Check for JSON editors
            const jsonEditors = element.querySelectorAll ? 
                element.querySelectorAll(CONFIG.selectors.jsonEditor) : [];
            jsonEditors.forEach(editor => this.injectJsonButtons(editor));

            // Check for system message fields
            const systemFields = element.querySelectorAll ? 
                element.querySelectorAll(CONFIG.selectors.systemMessage) : [];
            systemFields.forEach(field => this.injectPromptButtons(field));
        }

        // Process individual node
        processNode(node) {
            if (this.modifiedNodes.has(node)) return;

            const nodeType = this.getNodeType(node);
            const nodeId = this.getNodeId(node);

            // Register node
            this.nodeRegistry.set(nodeId, {
                element: node,
                type: nodeType,
                modified: Date.now()
            });

            // Apply modifications based on type
            this.applyNodeModifications(node, nodeType);
            
            // Mark as modified
            this.modifiedNodes.add(node);
            node.setAttribute('data-n8nboy-enhanced', 'true');

            console.log(`🔧 Modified node: ${nodeType} (${nodeId})`);
        }

        // Get node type from element
        getNodeType(node) {
            const typeElement = node.querySelector(CONFIG.selectors.nodeType);
            return typeElement ? typeElement.getAttribute('data-node-type') : 'unknown';
        }

        // Get node ID
        getNodeId(node) {
            return node.getAttribute('data-id') || 
                   node.id || 
                   `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        }

        // Apply modifications based on node type
        applyNodeModifications(node, nodeType) {
            // Apply universal modifications
            this.applyUniversalModifications(node);

            // Apply type-specific modifications
            switch (nodeType) {
                case CONFIG.nodeTypes.CODE:
                    this.modifyCodeNode(node);
                    break;
                case CONFIG.nodeTypes.AI_AGENT:
                    this.modifyAIAgentNode(node);
                    break;
                case CONFIG.nodeTypes.HTTP:
                    this.modifyHttpNode(node);
                    break;
                case CONFIG.nodeTypes.SET:
                    this.modifySetNode(node);
                    break;
                case CONFIG.nodeTypes.PARSER:
                    this.modifyParserNode(node);
                    break;
                default:
                    this.modifyGenericNode(node);
            }
        }

        // Apply universal modifications to all nodes
        applyUniversalModifications(node) {
            // Add hover effects
            node.style.transition = 'all 0.3s ease';
            
            // Add selection indicators
            this.addSelectionIndicator(node);
            
            // Add context menu
            this.addContextMenu(node);
            
            // Add custom data attributes
            node.setAttribute('data-n8nboy-version', '2.4');
            node.setAttribute('data-n8nboy-timestamp', Date.now());
        }

        // Modify code nodes
        modifyCodeNode(node) {
            node.style.boxShadow = CONFIG.styles.codeNode;
            node.setAttribute('data-n8nboy-type', 'code');
            
            // Add visual indicator
            this.addNodeIcon(node, '💻', '#ffff00');
        }

        // Modify AI Agent nodes
        modifyAIAgentNode(node) {
            node.style.boxShadow = CONFIG.styles.aiAgent;
            node.setAttribute('data-n8nboy-type', 'ai-agent');
            
            // Add visual indicator
            this.addNodeIcon(node, '🤖', '#00aaff');
        }

        // Modify HTTP nodes
        modifyHttpNode(node) {
            node.style.boxShadow = CONFIG.styles.httpNode;
            node.setAttribute('data-n8nboy-type', 'http');
            
            // Add visual indicator
            this.addNodeIcon(node, '🌐', '#00ff00');
        }

        // Modify Set nodes
        modifySetNode(node) {
            node.style.boxShadow = CONFIG.styles.setNode;
            node.setAttribute('data-n8nboy-type', 'set');
            
            // Add visual indicator
            this.addNodeIcon(node, '📝', '#ff00ff');
        }

        // Modify Parser nodes
        modifyParserNode(node) {
            node.style.boxShadow = CONFIG.styles.parserNode;
            node.setAttribute('data-n8nboy-type', 'parser');
            
            // Add visual indicator
            this.addNodeIcon(node, '🔍', '#ffa500');
        }

        // Modify generic nodes
        modifyGenericNode(node) {
            node.style.boxShadow = '0 0 5px rgba(255, 255, 255, 0.3)';
            node.setAttribute('data-n8nboy-type', 'generic');
            
            // Add visual indicator
            this.addNodeIcon(node, '⚙️', '#ffffff');
        }

        // Add visual icon to node
        addNodeIcon(node, icon, color) {
            // Check if icon already exists
            if (node.querySelector('.n8nboy-node-icon')) return;

            const iconElement = document.createElement('div');
            iconElement.className = 'n8nboy-node-icon';
            iconElement.innerHTML = icon;
            iconElement.style.cssText = `
                position: absolute;
                top: -8px;
                right: -8px;
                width: 20px;
                height: 20px;
                background: ${color};
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
                z-index: 1000;
                box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            `;
            
            node.style.position = 'relative';
            node.appendChild(iconElement);
        }

        // Add selection indicator
        addSelectionIndicator(node) {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                        if (node.classList.contains('selected')) {
                            this.onNodeSelected(node);
                        } else {
                            this.onNodeDeselected(node);
                        }
                    }
                });
            });

            observer.observe(node, {
                attributes: true,
                attributeFilter: ['class']
            });
        }

        // Handle node selection
        onNodeSelected(node) {
            node.style.borderColor = '#00ff41';
            node.style.borderWidth = '2px';
            console.log('🎯 Node selected:', this.getNodeType(node));
        }

        // Handle node deselection
        onNodeDeselected(node) {
            node.style.borderColor = '';
            node.style.borderWidth = '';
        }

        // Add context menu to node
        addContextMenu(node) {
            node.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                this.showNodeContextMenu(e, node);
            });
        }

        // Show context menu for node
        showNodeContextMenu(event, node) {
            const nodeType = this.getNodeType(node);
            console.log('📋 Context menu for:', nodeType);
            
            // You can implement custom context menu here
            // For now, just log the action
        }

        // Modify existing nodes on page
        modifyExistingNodes() {
            const existingNodes = document.querySelectorAll(CONFIG.selectors.nodes);
            console.log(`🔍 Found ${existingNodes.length} existing nodes`);
            
            existingNodes.forEach(node => {
                setTimeout(() => this.processNode(node), 50);
            });
        }

        // Setup global event listeners
        setupEventListeners() {
            // Listen for workflow changes
            document.addEventListener('n8n-workflow-changed', () => {
                console.log('🔄 Workflow changed, re-scanning nodes');
                setTimeout(() => this.modifyExistingNodes(), 1000);
            });

            // Listen for node updates
            document.addEventListener('n8n-node-update', (e) => {
                const nodeId = e.detail?.nodeId;
                if (nodeId) {
                    const nodeData = this.nodeRegistry.get(nodeId);
                    if (nodeData) {
                        this.processNode(nodeData.element);
                    }
                }
            });
        }

        // Get statistics
        getStats() {
            return {
                totalNodes: this.nodeRegistry.size,
                modifiedNodes: this.modifiedNodes.size,
                observers: this.observers.size,
                initialized: this.initialized
            };
        }

        // Cleanup function
        cleanup() {
            this.observers.forEach(observer => observer.disconnect());
            this.observers.clear();
            this.modifiedNodes.clear();
            this.nodeRegistry.clear();
            this.initialized = false;
            console.log('🧹 Universal Node Modifier System cleaned up');
        }
    }

    // Create global instance
    window.UniversalNodeModifier = new UniversalNodeModifier();

    // Auto-initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.UniversalNodeModifier.initialize();
        });
    } else {
        window.UniversalNodeModifier.initialize();
    }

    console.log('📦 Universal Node Modifier System loaded');

})();

/**
 * Advanced Node Detection and Classification System
 * Extends the Universal Node Modifier with sophisticated detection capabilities
 */

(function() {
    'use strict';

    // Advanced Node Detector Class
    class AdvancedNodeDetector {
        constructor() {
            this.nodePatterns = new Map();
            this.classificationRules = new Map();
            this.setupNodePatterns();
            this.setupClassificationRules();
        }

        // Setup node detection patterns
        setupNodePatterns() {
            this.nodePatterns.set('code', {
                selectors: [
                    '[data-node-type="n8n-nodes-base.code"]',
                    '[data-node-type="n8n-nodes-base.function"]',
                    '.code-node',
                    '[data-test-id*="code"]'
                ],
                indicators: ['jsCode', 'javascript', 'function', 'code'],
                priority: 10
            });

            this.nodePatterns.set('ai-agent', {
                selectors: [
                    '[data-node-type*="langchain.agent"]',
                    '[data-node-type*="openai"]',
                    '[data-node-type*="anthropic"]',
                    '.ai-agent-node'
                ],
                indicators: ['systemMessage', 'prompt', 'agent', 'ai'],
                priority: 9
            });

            this.nodePatterns.set('http', {
                selectors: [
                    '[data-node-type="n8n-nodes-base.httpRequest"]',
                    '[data-node-type*="webhook"]',
                    '.http-node'
                ],
                indicators: ['url', 'method', 'headers', 'http'],
                priority: 8
            });

            this.nodePatterns.set('database', {
                selectors: [
                    '[data-node-type*="mysql"]',
                    '[data-node-type*="postgres"]',
                    '[data-node-type*="mongodb"]',
                    '[data-node-type*="redis"]'
                ],
                indicators: ['query', 'database', 'sql', 'collection'],
                priority: 7
            });

            this.nodePatterns.set('trigger', {
                selectors: [
                    '[data-node-type*="trigger"]',
                    '[data-node-type*="cron"]',
                    '[data-node-type*="schedule"]'
                ],
                indicators: ['trigger', 'schedule', 'cron', 'interval'],
                priority: 6
            });
        }

        // Setup classification rules
        setupClassificationRules() {
            this.classificationRules.set('complexity', (node) => {
                const paramCount = this.getParameterCount(node);
                if (paramCount > 10) return 'high';
                if (paramCount > 5) return 'medium';
                return 'low';
            });

            this.classificationRules.set('connectivity', (node) => {
                const connections = this.getConnectionCount(node);
                if (connections > 3) return 'hub';
                if (connections > 1) return 'connected';
                return 'isolated';
            });

            this.classificationRules.set('status', (node) => {
                if (node.classList.contains('error')) return 'error';
                if (node.classList.contains('success')) return 'success';
                if (node.classList.contains('running')) return 'running';
                return 'idle';
            });
        }

        // Detect all nodes with advanced classification
        detectAllNodes() {
            const allNodes = document.querySelectorAll('.vue-flow__node');
            const detectedNodes = [];

            allNodes.forEach(node => {
                const classification = this.classifyNode(node);
                detectedNodes.push({
                    element: node,
                    ...classification
                });
            });

            return detectedNodes;
        }

        // Classify individual node
        classifyNode(node) {
            const nodeType = this.detectNodeType(node);
            const category = this.detectNodeCategory(node);
            const properties = this.analyzeNodeProperties(node);
            const metadata = this.extractNodeMetadata(node);

            return {
                type: nodeType,
                category: category,
                properties: properties,
                metadata: metadata,
                classification: this.generateClassification(node)
            };
        }

        // Detect specific node type
        detectNodeType(node) {
            // Try direct attribute first
            const typeAttr = node.querySelector('[data-node-type]');
            if (typeAttr) {
                return typeAttr.getAttribute('data-node-type');
            }

            // Try pattern matching
            for (const [type, pattern] of this.nodePatterns) {
                for (const selector of pattern.selectors) {
                    if (node.matches(selector) || node.querySelector(selector)) {
                        return type;
                    }
                }
            }

            return 'unknown';
        }

        // Detect node category
        detectNodeCategory(node) {
            const nodeType = this.detectNodeType(node);

            if (nodeType.includes('trigger')) return 'trigger';
            if (nodeType.includes('langchain') || nodeType.includes('ai')) return 'ai';
            if (nodeType.includes('http') || nodeType.includes('webhook')) return 'communication';
            if (nodeType.includes('database') || nodeType.includes('sql')) return 'data';
            if (nodeType.includes('code') || nodeType.includes('function')) return 'logic';
            if (nodeType.includes('transform') || nodeType.includes('set')) return 'transform';

            return 'utility';
        }

        // Analyze node properties
        analyzeNodeProperties(node) {
            return {
                id: this.getNodeId(node),
                name: this.getNodeName(node),
                position: this.getNodePosition(node),
                size: this.getNodeSize(node),
                connections: this.getConnectionCount(node),
                parameters: this.getParameterCount(node),
                hasErrors: this.hasErrors(node),
                isSelected: node.classList.contains('selected'),
                isDisabled: node.classList.contains('disabled')
            };
        }

        // Extract node metadata
        extractNodeMetadata(node) {
            return {
                version: node.getAttribute('data-node-version') || 'unknown',
                created: node.getAttribute('data-created') || Date.now(),
                modified: node.getAttribute('data-modified') || Date.now(),
                tags: this.extractTags(node),
                description: this.getNodeDescription(node)
            };
        }

        // Generate comprehensive classification
        generateClassification(node) {
            const classification = {};

            for (const [rule, classifier] of this.classificationRules) {
                classification[rule] = classifier(node);
            }

            return classification;
        }

        // Helper methods
        getNodeId(node) {
            return node.getAttribute('data-id') ||
                   node.id ||
                   `node-${Math.random().toString(36).substr(2, 9)}`;
        }

        getNodeName(node) {
            const nameElement = node.querySelector('.node-name, .node-title, [data-test-id="node-title"]');
            return nameElement ? nameElement.textContent.trim() : 'Unnamed Node';
        }

        getNodePosition(node) {
            const rect = node.getBoundingClientRect();
            return {
                x: rect.left,
                y: rect.top,
                width: rect.width,
                height: rect.height
            };
        }

        getNodeSize(node) {
            const rect = node.getBoundingClientRect();
            return {
                width: rect.width,
                height: rect.height,
                area: rect.width * rect.height
            };
        }

        getConnectionCount(node) {
            const connections = node.querySelectorAll('.vue-flow__handle');
            return connections.length;
        }

        getParameterCount(node) {
            // This would need to access n8n's internal state
            // For now, estimate based on visible elements
            const inputs = node.querySelectorAll('input, select, textarea');
            return inputs.length;
        }

        hasErrors(node) {
            return node.classList.contains('error') ||
                   node.querySelector('.error, .has-error') !== null;
        }

        extractTags(node) {
            const tags = [];
            const classList = Array.from(node.classList);

            classList.forEach(className => {
                if (className.startsWith('tag-') || className.startsWith('category-')) {
                    tags.push(className);
                }
            });

            return tags;
        }

        getNodeDescription(node) {
            const descElement = node.querySelector('.node-description, .description, [data-test-id="node-description"]');
            return descElement ? descElement.textContent.trim() : '';
        }

        // Bulk operations
        getNodesByType(nodeType) {
            return this.detectAllNodes().filter(node => node.type === nodeType);
        }

        getNodesByCategory(category) {
            return this.detectAllNodes().filter(node => node.category === category);
        }

        getNodesByClassification(rule, value) {
            return this.detectAllNodes().filter(node =>
                node.classification[rule] === value
            );
        }

        // Statistics
        getDetectionStats() {
            const allNodes = this.detectAllNodes();
            const stats = {
                total: allNodes.length,
                byType: {},
                byCategory: {},
                byStatus: {}
            };

            allNodes.forEach(node => {
                // Count by type
                stats.byType[node.type] = (stats.byType[node.type] || 0) + 1;

                // Count by category
                stats.byCategory[node.category] = (stats.byCategory[node.category] || 0) + 1;

                // Count by status
                const status = node.classification.status;
                stats.byStatus[status] = (stats.byStatus[status] || 0) + 1;
            });

            return stats;
        }
    }

    // Extend the Universal Node Modifier with advanced detection
    if (window.UniversalNodeModifier) {
        window.UniversalNodeModifier.detector = new AdvancedNodeDetector();

        // Add detection methods to the main modifier
        window.UniversalNodeModifier.detectAllNodes = function() {
            return this.detector.detectAllNodes();
        };

        window.UniversalNodeModifier.classifyNode = function(node) {
            return this.detector.classifyNode(node);
        };

        window.UniversalNodeModifier.getNodesByType = function(type) {
            return this.detector.getNodesByType(type);
        };

        window.UniversalNodeModifier.getDetectionStats = function() {
            return this.detector.getDetectionStats();
        };

        console.log('🔍 Advanced Node Detection System integrated');
    }

})();
