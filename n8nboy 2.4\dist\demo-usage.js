/**
 * n8nboy Universal Node Modifier - Demo Usage Examples
 * This file demonstrates how to use all the new node modification features
 */

// ============================================================================
// BASIC USAGE EXAMPLES
// ============================================================================

// 1. Get all nodes and their classifications
function demoBasicNodeDetection() {
    console.log('=== Basic Node Detection Demo ===');
    
    // Get all nodes with full classification
    const allNodes = window.n8nboy.modifier().detectAllNodes();
    console.log(`Found ${allNodes.length} nodes:`, allNodes);
    
    // Get nodes by specific type
    const codeNodes = window.n8nboy.modifier().getNodesByType('n8n-nodes-base.code');
    console.log(`Code nodes:`, codeNodes);
    
    // Get detection statistics
    const stats = window.n8nboy.modifier().getDetectionStats();
    console.log('Detection statistics:', stats);
}

// 2. Apply visual modifications to all nodes
function demoVisualModifications() {
    console.log('=== Visual Modifications Demo ===');
    
    // Highlight all nodes with green glow
    window.n8nboy.bulkOps().applyToAllNodes('highlight', {
        color: '#00ff41',
        intensity: 0.8
    }).then(result => {
        console.log('Highlighted all nodes:', result);
    });
    
    // Dim all HTTP nodes
    window.n8nboy.bulkOps().applyToNodesByType('n8n-nodes-base.httpRequest', 'dim', {
        opacity: 0.5
    }).then(result => {
        console.log('Dimmed HTTP nodes:', result);
    });
    
    // Add icons to AI agent nodes
    window.n8nboy.bulkOps().applyToNodesByType('@n8n/n8n-nodes-langchain.agent', 'addIcon', {
        icon: '🤖',
        color: '#00aaff'
    }).then(result => {
        console.log('Added icons to AI nodes:', result);
    });
}

// 3. Filter and modify specific node groups
function demoAdvancedFiltering() {
    console.log('=== Advanced Filtering Demo ===');
    
    // Filter nodes by multiple criteria
    const complexNodes = window.n8nboy.bulkOps().filterNodes({
        byCategory: 'ai',
        byStatus: 'idle',
        bySelection: false
    });
    console.log('Complex AI nodes:', complexNodes);
    
    // Apply modifications to filtered nodes
    window.n8nboy.bulkOps().applyToFilteredNodes(
        { byError: true }, // Nodes with errors
        'addBorder',
        { color: '#ff0000', width: '3px', style: 'dashed' }
    ).then(result => {
        console.log('Added error borders:', result);
    });
    
    // Filter by custom predicate
    const largeNodes = window.n8nboy.bulkOps().filterNodes({
        byCustom: (node) => {
            const rect = node.element.getBoundingClientRect();
            return rect.width > 200 || rect.height > 100;
        }
    });
    console.log('Large nodes:', largeNodes);
}

// ============================================================================
// REAL-TIME MONITORING EXAMPLES
// ============================================================================

// 4. Setup real-time monitoring with custom handlers
function demoRealtimeMonitoring() {
    console.log('=== Real-time Monitoring Demo ===');
    
    const monitor = window.n8nboy.monitor();
    
    // Listen for new nodes being added
    monitor.addEventListener('nodeAdded', (event) => {
        const { node, nodeId } = event.detail;
        console.log(`New node added: ${nodeId}`);
        
        // Automatically enhance new nodes
        setTimeout(() => {
            window.n8nboy.modifier().processNode(node);
        }, 100);
    });
    
    // Listen for node selection changes
    monitor.addEventListener('nodeSelected', (event) => {
        const { nodeData } = event.detail;
        console.log(`Node selected: ${nodeData.id} (${nodeData.type})`);
        
        // Highlight selected node
        window.n8nboy.bulkOps().applyToFilteredNodes(
            { byCustom: (n) => n.id === nodeData.id },
            'highlight',
            { color: '#ffff00', intensity: 1.0 }
        );
    });
    
    // Listen for editor events
    monitor.addEventListener('editorOpened', (event) => {
        const { editorData } = event.detail;
        console.log(`Editor opened for: ${editorData.nodeType}`);
    });
    
    // Get monitoring status
    const status = monitor.getMonitoringStatus();
    console.log('Monitoring status:', status);
}

// ============================================================================
// BULK OPERATIONS EXAMPLES
// ============================================================================

// 5. Advanced bulk operations
function demoBulkOperations() {
    console.log('=== Bulk Operations Demo ===');
    
    const bulkOps = window.n8nboy.bulkOps();
    
    // Reset all modifications
    bulkOps.applyToAllNodes('reset').then(() => {
        console.log('All nodes reset');
        
        // Apply type-specific enhancements
        return Promise.all([
            // Code nodes: Yellow glow + code icon
            bulkOps.applyToNodesByType('n8n-nodes-base.code', 'highlight', {
                color: '#ffff00',
                intensity: 0.6
            }),
            
            // AI nodes: Blue glow + robot icon
            bulkOps.applyToNodesByCategory('ai', 'highlight', {
                color: '#00aaff',
                intensity: 0.8
            }),
            
            // HTTP nodes: Green glow + globe icon
            bulkOps.applyToNodesByType('n8n-nodes-base.httpRequest', 'highlight', {
                color: '#00ff00',
                intensity: 0.7
            })
        ]);
    }).then(() => {
        console.log('Type-specific enhancements applied');
    });
    
    // Get operation status
    const opStatus = bulkOps.getOperationStatus();
    console.log('Operation status:', opStatus);
}

// 6. Node analysis and debugging
function demoNodeAnalysis() {
    console.log('=== Node Analysis Demo ===');
    
    // Validate all nodes
    window.n8nboy.bulkOps().applyToAllNodes('validate').then(result => {
        console.log('Validation results:', result);
        
        // Find invalid nodes
        const invalidNodes = result.results.filter(r => !r.result?.isValid);
        console.log('Invalid nodes:', invalidNodes);
    });
    
    // Debug specific node types
    window.n8nboy.bulkOps().applyToNodesByType('n8n-nodes-base.code', 'debug').then(result => {
        console.log('Code node debug info:', result);
    });
    
    // Optimize all nodes
    window.n8nboy.bulkOps().applyToAllNodes('optimize').then(result => {
        console.log('Optimization results:', result);
    });
}

// ============================================================================
// KEYBOARD SHORTCUTS AND AUTOMATION
// ============================================================================

// 7. Setup custom keyboard shortcuts
function demoKeyboardShortcuts() {
    console.log('=== Keyboard Shortcuts Demo ===');
    
    document.addEventListener('keydown', (event) => {
        // Ctrl+Alt+1: Highlight all code nodes
        if (event.ctrlKey && event.altKey && event.key === '1') {
            event.preventDefault();
            window.n8nboy.bulkOps().applyToNodesByType('n8n-nodes-base.code', 'highlight', {
                color: '#ffff00'
            });
            console.log('Code nodes highlighted');
        }
        
        // Ctrl+Alt+2: Highlight all AI nodes
        if (event.ctrlKey && event.altKey && event.key === '2') {
            event.preventDefault();
            window.n8nboy.bulkOps().applyToNodesByCategory('ai', 'highlight', {
                color: '#00aaff'
            });
            console.log('AI nodes highlighted');
        }
        
        // Ctrl+Alt+R: Reset all nodes
        if (event.ctrlKey && event.altKey && event.key === 'r') {
            event.preventDefault();
            window.n8nboy.resetAll();
            console.log('All nodes reset');
        }
        
        // Ctrl+Alt+S: Show statistics
        if (event.ctrlKey && event.altKey && event.key === 's') {
            event.preventDefault();
            const stats = window.n8nboy.stats();
            console.table(stats);
        }
    });
    
    console.log('Custom keyboard shortcuts registered:');
    console.log('- Ctrl+Alt+1: Highlight code nodes');
    console.log('- Ctrl+Alt+2: Highlight AI nodes');
    console.log('- Ctrl+Alt+R: Reset all nodes');
    console.log('- Ctrl+Alt+S: Show statistics');
}

// ============================================================================
// AUTOMATION AND WORKFLOWS
// ============================================================================

// 8. Automated workflow enhancement
function demoAutomatedEnhancement() {
    console.log('=== Automated Enhancement Demo ===');
    
    // Create an automated enhancement workflow
    const enhancementWorkflow = async () => {
        console.log('Starting automated enhancement...');
        
        // Step 1: Reset all nodes
        await window.n8nboy.bulkOps().applyToAllNodes('reset');
        console.log('✓ Reset complete');
        
        // Step 2: Apply category-based styling
        const categoryStyles = {
            'trigger': { color: '#ff6b6b', icon: '⚡' },
            'ai': { color: '#4ecdc4', icon: '🤖' },
            'communication': { color: '#45b7d1', icon: '🌐' },
            'data': { color: '#96ceb4', icon: '💾' },
            'logic': { color: '#ffeaa7', icon: '⚙️' },
            'utility': { color: '#dda0dd', icon: '🔧' }
        };
        
        for (const [category, style] of Object.entries(categoryStyles)) {
            await window.n8nboy.bulkOps().applyToNodesByCategory(category, 'highlight', {
                color: style.color,
                intensity: 0.6
            });
            
            await window.n8nboy.bulkOps().applyToNodesByCategory(category, 'addIcon', {
                icon: style.icon,
                color: style.color
            });
        }
        console.log('✓ Category styling complete');
        
        // Step 3: Add error indicators
        await window.n8nboy.bulkOps().applyToFilteredNodes(
            { byError: true },
            'addBorder',
            { color: '#ff0000', width: '2px', style: 'solid' }
        );
        console.log('✓ Error indicators added');
        
        // Step 4: Add tooltips
        const allNodes = window.n8nboy.modifier().detectAllNodes();
        for (const node of allNodes) {
            await window.n8nboy.bulkOps().applyToFilteredNodes(
                { byCustom: (n) => n.id === node.id },
                'addTooltip',
                { text: `${node.type} - ${node.category}` }
            );
        }
        console.log('✓ Tooltips added');
        
        console.log('🎉 Automated enhancement complete!');
    };
    
    // Run the workflow
    enhancementWorkflow().catch(error => {
        console.error('Enhancement workflow failed:', error);
    });
}

// ============================================================================
// SYSTEM MONITORING AND HEALTH
// ============================================================================

// 9. System health monitoring
function demoSystemHealth() {
    console.log('=== System Health Demo ===');
    
    // Get comprehensive system statistics
    const stats = window.n8nboy.stats();
    console.log('System Statistics:');
    console.table(stats);
    
    // Perform health check
    window.n8nboy.health().then(healthReport => {
        console.log('Health Report:', healthReport);
        
        if (healthReport.overall !== 'healthy') {
            console.warn('System health issues detected:', healthReport.issues);
        } else {
            console.log('✅ All systems healthy');
        }
    });
    
    // Monitor performance
    setInterval(() => {
        const currentStats = window.n8nboy.stats();
        const nodeCount = currentStats.integration?.nodesProcessed || 0;
        const errors = currentStats.integration?.errorsEncountered || 0;
        
        if (errors > 10) {
            console.warn(`High error count detected: ${errors}`);
        }
        
        if (nodeCount > 100) {
            console.log(`High node processing activity: ${nodeCount} nodes processed`);
        }
    }, 5000);
}

// ============================================================================
// DEMO RUNNER
// ============================================================================

// Main demo function
function runAllDemos() {
    console.log('🚀 Starting n8nboy Universal Node Modifier Demos');
    console.log('Open the browser console to see all output');
    
    // Wait for system to be ready
    if (window.n8nboy && window.n8nboy.integration.initialized) {
        runDemos();
    } else {
        document.addEventListener('integrationReady', runDemos);
    }
    
    function runDemos() {
        console.log('✅ System ready - running demos...');
        
        // Run demos with delays to see effects
        setTimeout(demoBasicNodeDetection, 1000);
        setTimeout(demoRealtimeMonitoring, 2000);
        setTimeout(demoKeyboardShortcuts, 3000);
        setTimeout(demoVisualModifications, 4000);
        setTimeout(demoAdvancedFiltering, 6000);
        setTimeout(demoBulkOperations, 8000);
        setTimeout(demoNodeAnalysis, 10000);
        setTimeout(demoSystemHealth, 12000);
        setTimeout(demoAutomatedEnhancement, 14000);
        
        console.log('📋 All demos scheduled - watch the console for results!');
    }
}

// Auto-run demos if this script is loaded
if (typeof window !== 'undefined') {
    // Expose demo functions globally for manual testing
    window.n8nDemos = {
        runAll: runAllDemos,
        basicDetection: demoBasicNodeDetection,
        visualMods: demoVisualModifications,
        filtering: demoAdvancedFiltering,
        monitoring: demoRealtimeMonitoring,
        bulkOps: demoBulkOperations,
        analysis: demoNodeAnalysis,
        shortcuts: demoKeyboardShortcuts,
        automation: demoAutomatedEnhancement,
        health: demoSystemHealth
    };
    
    console.log('📦 Demo functions loaded. Use window.n8nDemos.runAll() to start all demos');
    console.log('Or run individual demos: window.n8nDemos.basicDetection(), etc.');
}

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        runAllDemos,
        demoBasicNodeDetection,
        demoVisualModifications,
        demoAdvancedFiltering,
        demoRealtimeMonitoring,
        demoBulkOperations,
        demoNodeAnalysis,
        demoKeyboardShortcuts,
        demoAutomatedEnhancement,
        demoSystemHealth
    };
}
