/**
 * Editor Button Injection System for n8nboy Extension
 * Dynamically injects custom buttons into node editors based on content and type
 */

(function() {
    'use strict';

    // Button injection configuration
    const BUTTON_CONFIG = {
        styles: {
            base: `
                background: linear-gradient(135deg, #00ff41, #00cc33);
                border: none;
                border-radius: 4px;
                color: #000;
                cursor: pointer;
                font-family: 'Courier New', monospace;
                font-size: 11px;
                font-weight: bold;
                margin: 2px;
                padding: 6px 12px;
                transition: all 0.2s ease;
                z-index: 1000;
                position: relative;
                box-shadow: 0 2px 4px rgba(0, 255, 65, 0.3);
            `,
            hover: `
                background: linear-gradient(135deg, #00cc33, #00aa2a);
                box-shadow: 0 4px 8px rgba(0, 255, 65, 0.5);
                transform: translateY(-1px);
            `,
            disabled: `
                background: #666;
                color: #999;
                cursor: not-allowed;
                opacity: 0.5;
            `
        },
        types: {
            generate: { color: '#00ff41', icon: '⚡' },
            optimize: { color: '#9c27b0', icon: '🔧' },
            fix: { color: '#2196f3', icon: '🔨' },
            analyze: { color: '#ff9800', icon: '🔍' },
            export: { color: '#4caf50', icon: '📤' }
        }
    };

    // Editor Button Injection System
    class EditorButtonInjector {
        constructor() {
            this.injectedButtons = new Map();
            this.contentObservers = new Map();
            this.buttonHandlers = new Map();
            this.setupButtonHandlers();
        }

        // Setup button click handlers
        setupButtonHandlers() {
            this.buttonHandlers.set('generate-code', this.handleGenerateCode.bind(this));
            this.buttonHandlers.set('generate-prompt', this.handleGeneratePrompt.bind(this));
            this.buttonHandlers.set('optimize-prompt', this.handleOptimizePrompt.bind(this));
            this.buttonHandlers.set('generate-json', this.handleGenerateJson.bind(this));
            this.buttonHandlers.set('fix-json', this.handleFixJson.bind(this));
            this.buttonHandlers.set('generate-curl', this.handleGenerateCurl.bind(this));
            this.buttonHandlers.set('analyze-error', this.handleAnalyzeError.bind(this));
        }

        // Main injection method
        injectButtons(editor, nodeType, contentType) {
            const editorId = this.getEditorId(editor);
            
            if (this.injectedButtons.has(editorId)) {
                this.updateExistingButtons(editorId, editor);
                return;
            }

            const buttonContainer = this.createButtonContainer(editorId);
            const buttons = this.determineButtons(nodeType, contentType, editor);
            
            buttons.forEach(buttonConfig => {
                const button = this.createButton(buttonConfig);
                buttonContainer.appendChild(button);
            });

            this.insertButtonContainer(editor, buttonContainer);
            this.setupContentMonitoring(editor, editorId);
            this.injectedButtons.set(editorId, { container: buttonContainer, buttons });

            console.log(`🔘 Injected ${buttons.length} buttons for ${nodeType}/${contentType}`);
        }

        // Determine which buttons to inject
        determineButtons(nodeType, contentType, editor) {
            const buttons = [];

            switch (nodeType) {
                case 'n8n-nodes-base.code':
                    buttons.push({
                        id: 'generate-code',
                        text: 'Generate Code',
                        type: 'generate',
                        enabled: this.hasContent(editor)
                    });
                    break;

                case '@n8n/n8n-nodes-langchain.agent':
                    if (this.isSystemMessageField(editor)) {
                        buttons.push({
                            id: 'generate-prompt',
                            text: 'Generate Prompt',
                            type: 'generate',
                            enabled: this.hasContent(editor)
                        });
                        buttons.push({
                            id: 'optimize-prompt',
                            text: 'Optimize Prompt',
                            type: 'optimize',
                            enabled: this.hasContent(editor)
                        });
                    }
                    break;

                case 'n8n-nodes-base.set':
                case '@n8n/n8n-nodes-langchain.outputParserStructured':
                    if (this.isJsonField(editor)) {
                        buttons.push({
                            id: 'generate-json',
                            text: 'Generate JSON',
                            type: 'generate',
                            enabled: true
                        });
                        buttons.push({
                            id: 'fix-json',
                            text: 'Fix JSON',
                            type: 'fix',
                            enabled: this.hasContent(editor)
                        });
                    }
                    break;

                case 'n8n-nodes-base.httpRequest':
                    buttons.push({
                        id: 'generate-curl',
                        text: 'Generate cURL',
                        type: 'generate',
                        enabled: this.hasContent(editor)
                    });
                    break;
            }

            // Universal buttons for all editors
            if (this.hasErrorContent(editor)) {
                buttons.push({
                    id: 'analyze-error',
                    text: 'Analyze Error',
                    type: 'analyze',
                    enabled: true
                });
            }

            return buttons;
        }

        // Create button element
        createButton(config) {
            const button = document.createElement('button');
            button.className = 'n8nboy-editor-button';
            button.setAttribute('data-button-id', config.id);
            button.setAttribute('data-button-type', config.type);
            
            const typeConfig = BUTTON_CONFIG.types[config.type];
            button.innerHTML = `${typeConfig.icon} ${config.text}`;
            
            // Apply styles
            button.style.cssText = BUTTON_CONFIG.styles.base;
            if (typeConfig.color) {
                button.style.background = `linear-gradient(135deg, ${typeConfig.color}, ${this.darkenColor(typeConfig.color)})`;
            }
            
            if (!config.enabled) {
                button.style.cssText += BUTTON_CONFIG.styles.disabled;
                button.disabled = true;
            }

            // Add event listeners
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.handleButtonClick(config.id, button);
            });

            button.addEventListener('mouseenter', () => {
                if (!button.disabled) {
                    button.style.cssText += BUTTON_CONFIG.styles.hover;
                }
            });

            button.addEventListener('mouseleave', () => {
                if (!button.disabled) {
                    button.style.cssText = BUTTON_CONFIG.styles.base;
                    if (typeConfig.color) {
                        button.style.background = `linear-gradient(135deg, ${typeConfig.color}, ${this.darkenColor(typeConfig.color)})`;
                    }
                }
            });

            return button;
        }

        // Create button container
        createButtonContainer(editorId) {
            const container = document.createElement('div');
            container.className = 'n8nboy-button-container';
            container.setAttribute('data-editor-id', editorId);
            container.style.cssText = `
                display: flex;
                flex-wrap: wrap;
                gap: 4px;
                margin: 8px 0;
                padding: 8px;
                background: rgba(0, 0, 0, 0.05);
                border-radius: 4px;
                border: 1px solid rgba(0, 255, 65, 0.2);
                position: relative;
                z-index: 100;
            `;
            return container;
        }

        // Insert button container into editor
        insertButtonContainer(editor, container) {
            // Try different insertion strategies
            const insertionPoints = [
                editor.querySelector('.cm-editor'),
                editor.querySelector('.monaco-editor'),
                editor.querySelector('textarea'),
                editor.querySelector('.parameter-input'),
                editor.parentElement
            ];

            for (const point of insertionPoints) {
                if (point) {
                    if (point.parentElement) {
                        point.parentElement.insertBefore(container, point.nextSibling);
                        return;
                    }
                }
            }

            // Fallback: append to editor
            editor.appendChild(container);
        }

        // Setup content monitoring for dynamic button updates
        setupContentMonitoring(editor, editorId) {
            const observer = new MutationObserver(() => {
                this.updateButtonStates(editorId, editor);
            });

            observer.observe(editor, {
                childList: true,
                subtree: true,
                characterData: true
            });

            // Also monitor input events
            editor.addEventListener('input', () => {
                this.updateButtonStates(editorId, editor);
            });

            this.contentObservers.set(editorId, observer);
        }

        // Update button states based on content
        updateButtonStates(editorId, editor) {
            const buttonData = this.injectedButtons.get(editorId);
            if (!buttonData) return;

            const hasContent = this.hasContent(editor);
            const hasError = this.hasErrorContent(editor);

            buttonData.container.querySelectorAll('.n8nboy-editor-button').forEach(button => {
                const buttonId = button.getAttribute('data-button-id');
                
                switch (buttonId) {
                    case 'generate-code':
                    case 'optimize-prompt':
                    case 'fix-json':
                    case 'generate-curl':
                        this.setButtonEnabled(button, hasContent);
                        break;
                    case 'analyze-error':
                        this.setButtonEnabled(button, hasError);
                        break;
                    case 'generate-prompt':
                    case 'generate-json':
                        this.setButtonEnabled(button, true);
                        break;
                }
            });
        }

        // Set button enabled/disabled state
        setButtonEnabled(button, enabled) {
            button.disabled = !enabled;
            if (enabled) {
                button.style.cssText = BUTTON_CONFIG.styles.base;
                const type = button.getAttribute('data-button-type');
                const typeConfig = BUTTON_CONFIG.types[type];
                if (typeConfig.color) {
                    button.style.background = `linear-gradient(135deg, ${typeConfig.color}, ${this.darkenColor(typeConfig.color)})`;
                }
            } else {
                button.style.cssText += BUTTON_CONFIG.styles.disabled;
            }
        }

        // Handle button clicks
        handleButtonClick(buttonId, buttonElement) {
            const handler = this.buttonHandlers.get(buttonId);
            if (handler) {
                this.showButtonLoading(buttonElement);
                handler(buttonElement).finally(() => {
                    this.hideButtonLoading(buttonElement);
                });
            }
        }

        // Show loading state
        showButtonLoading(button) {
            button.originalText = button.innerHTML;
            button.innerHTML = '⏳ Processing...';
            button.disabled = true;
        }

        // Hide loading state
        hideButtonLoading(button) {
            button.innerHTML = button.originalText;
            button.disabled = false;
        }

        // Content detection methods
        hasContent(editor) {
            const content = this.getEditorContent(editor);
            return content && content.trim().length > 0;
        }

        hasErrorContent(editor) {
            const content = this.getEditorContent(editor);
            return content && (
                content.includes('error') ||
                content.includes('Error') ||
                content.includes('ERROR') ||
                content.includes('failed') ||
                content.includes('exception')
            );
        }

        isSystemMessageField(editor) {
            return editor.closest('[data-test-id*="systemMessage"]') !== null ||
                   editor.closest('[data-parameter-name="systemMessage"]') !== null;
        }

        isJsonField(editor) {
            return editor.closest('[data-test-id*="json"]') !== null ||
                   editor.querySelector('[data-language="json"]') !== null ||
                   editor.closest('[data-parameter-name*="json"]') !== null;
        }

        getEditorContent(editor) {
            // Try different methods to get content
            const methods = [
                () => editor.querySelector('.cm-content')?.textContent,
                () => editor.querySelector('textarea')?.value,
                () => editor.querySelector('input')?.value,
                () => editor.textContent
            ];

            for (const method of methods) {
                try {
                    const content = method();
                    if (content !== undefined && content !== null) {
                        return content;
                    }
                } catch (e) {
                    // Continue to next method
                }
            }

            return '';
        }

        getEditorId(editor) {
            return editor.id || 
                   editor.getAttribute('data-editor-id') ||
                   `editor-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        }

        // Utility methods
        darkenColor(color) {
            // Simple color darkening
            if (color.startsWith('#')) {
                const num = parseInt(color.slice(1), 16);
                const amt = -20;
                const R = (num >> 16) + amt;
                const G = (num >> 8 & 0x00FF) + amt;
                const B = (num & 0x0000FF) + amt;
                return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                    (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
            }
            return color;
        }

        // Button handler methods (to be implemented)
        async handleGenerateCode(button) {
            console.log('🔧 Generate Code clicked');
            // Implementation will be added in the next file
        }

        async handleGeneratePrompt(button) {
            console.log('🤖 Generate Prompt clicked');
            // Implementation will be added in the next file
        }

        async handleOptimizePrompt(button) {
            console.log('⚡ Optimize Prompt clicked');
            // Implementation will be added in the next file
        }

        async handleGenerateJson(button) {
            console.log('📝 Generate JSON clicked');
            // Implementation will be added in the next file
        }

        async handleFixJson(button) {
            console.log('🔨 Fix JSON clicked');
            // Implementation will be added in the next file
        }

        async handleGenerateCurl(button) {
            console.log('🌐 Generate cURL clicked');
            // Implementation will be added in the next file
        }

        async handleAnalyzeError(button) {
            console.log('🔍 Analyze Error clicked');
            // Implementation will be added in the next file
        }

        // Cleanup
        cleanup() {
            this.contentObservers.forEach(observer => observer.disconnect());
            this.contentObservers.clear();
            this.injectedButtons.clear();
            console.log('🧹 Editor Button Injector cleaned up');
        }
    }

    // Create global instance
    window.EditorButtonInjector = new EditorButtonInjector();

    // Integrate with Universal Node Modifier
    if (window.UniversalNodeModifier) {
        window.UniversalNodeModifier.buttonInjector = window.EditorButtonInjector;
        
        // Add injection methods to the main modifier
        window.UniversalNodeModifier.injectEditorButtons = function(editor, nodeType, contentType) {
            return this.buttonInjector.injectButtons(editor, nodeType, contentType);
        };
        
        console.log('🔘 Editor Button Injection System integrated');
    }

    console.log('📦 Editor Button Injection System loaded');

})();
