/**
 * Real-time Node Monitoring System for n8nboy Extension
 * Advanced monitoring with MutationObserver for nodes, selections, and editor states
 */

(function() {
    'use strict';

    // Monitoring configuration
    const MONITOR_CONFIG = {
        intervals: {
            nodeCheck: 1000,
            editorCheck: 500,
            performanceCheck: 5000
        },
        thresholds: {
            maxNodes: 100,
            maxObservers: 50,
            memoryLimit: 50 * 1024 * 1024 // 50MB
        },
        selectors: {
            workflow: '.vue-flow',
            nodes: '.vue-flow__node',
            edges: '.vue-flow__edge',
            editor: '.ndv-wrapper',
            canvas: '.vue-flow__viewport'
        }
    };

    // Real-time Monitoring System
    class RealtimeMonitor {
        constructor() {
            this.observers = new Map();
            this.monitoringState = {
                nodes: new Map(),
                editors: new Map(),
                selections: new Set(),
                performance: {
                    nodeCount: 0,
                    observerCount: 0,
                    memoryUsage: 0,
                    lastUpdate: Date.now()
                }
            };
            this.eventHandlers = new Map();
            this.isMonitoring = false;
            this.setupEventHandlers();
        }

        // Setup event handlers
        setupEventHandlers() {
            this.eventHandlers.set('nodeAdded', []);
            this.eventHandlers.set('nodeRemoved', []);
            this.eventHandlers.set('nodeSelected', []);
            this.eventHandlers.set('nodeDeselected', []);
            this.eventHandlers.set('editorOpened', []);
            this.eventHandlers.set('editorClosed', []);
            this.eventHandlers.set('workflowChanged', []);
            this.eventHandlers.set('performanceAlert', []);
        }

        // Start monitoring
        startMonitoring() {
            if (this.isMonitoring) return;

            console.log('🔍 Starting Real-time Monitoring System');
            
            this.setupWorkflowMonitoring();
            this.setupNodeMonitoring();
            this.setupEditorMonitoring();
            this.setupSelectionMonitoring();
            this.setupPerformanceMonitoring();
            
            this.isMonitoring = true;
            console.log('✅ Real-time monitoring active');
        }

        // Stop monitoring
        stopMonitoring() {
            if (!this.isMonitoring) return;

            console.log('🛑 Stopping Real-time Monitoring System');
            
            this.observers.forEach(observer => observer.disconnect());
            this.observers.clear();
            
            this.isMonitoring = false;
            console.log('⏹️ Real-time monitoring stopped');
        }

        // Setup workflow-level monitoring
        setupWorkflowMonitoring() {
            const workflow = document.querySelector(MONITOR_CONFIG.selectors.workflow);
            if (!workflow) return;

            const observer = new MutationObserver((mutations) => {
                let workflowChanged = false;
                
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach((node) => {
                            if (this.isNodeElement(node)) {
                                this.handleNodeAdded(node);
                                workflowChanged = true;
                            }
                        });
                        
                        mutation.removedNodes.forEach((node) => {
                            if (this.isNodeElement(node)) {
                                this.handleNodeRemoved(node);
                                workflowChanged = true;
                            }
                        });
                    }
                });
                
                if (workflowChanged) {
                    this.triggerEvent('workflowChanged', { timestamp: Date.now() });
                }
            });

            observer.observe(workflow, {
                childList: true,
                subtree: true
            });

            this.observers.set('workflow', observer);
            console.log('📊 Workflow monitoring setup');
        }

        // Setup individual node monitoring
        setupNodeMonitoring() {
            const existingNodes = document.querySelectorAll(MONITOR_CONFIG.selectors.nodes);
            
            existingNodes.forEach(node => {
                this.monitorNode(node);
            });

            console.log(`🎯 Monitoring ${existingNodes.length} existing nodes`);
        }

        // Monitor individual node
        monitorNode(node) {
            const nodeId = this.getNodeId(node);
            
            if (this.monitoringState.nodes.has(nodeId)) return;

            // Create node monitoring data
            const nodeData = {
                id: nodeId,
                element: node,
                type: this.getNodeType(node),
                selected: false,
                lastUpdate: Date.now(),
                changeCount: 0
            };

            // Setup attribute observer for selection changes
            const attributeObserver = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.attributeName === 'class') {
                        const isSelected = node.classList.contains('selected');
                        
                        if (isSelected !== nodeData.selected) {
                            nodeData.selected = isSelected;
                            nodeData.lastUpdate = Date.now();
                            nodeData.changeCount++;
                            
                            if (isSelected) {
                                this.handleNodeSelected(node, nodeData);
                            } else {
                                this.handleNodeDeselected(node, nodeData);
                            }
                        }
                    }
                });
            });

            attributeObserver.observe(node, {
                attributes: true,
                attributeFilter: ['class']
            });

            // Setup content observer for node changes
            const contentObserver = new MutationObserver(() => {
                nodeData.lastUpdate = Date.now();
                nodeData.changeCount++;
                this.updateNodeState(nodeId, nodeData);
            });

            contentObserver.observe(node, {
                childList: true,
                subtree: true,
                characterData: true
            });

            // Store monitoring data
            this.monitoringState.nodes.set(nodeId, nodeData);
            this.observers.set(`node-${nodeId}`, attributeObserver);
            this.observers.set(`node-content-${nodeId}`, contentObserver);
        }

        // Setup editor monitoring
        setupEditorMonitoring() {
            const editorObserver = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    mutation.addedNodes.forEach((node) => {
                        if (this.isEditorElement(node)) {
                            this.handleEditorOpened(node);
                        }
                    });
                    
                    mutation.removedNodes.forEach((node) => {
                        if (this.isEditorElement(node)) {
                            this.handleEditorClosed(node);
                        }
                    });
                });
            });

            editorObserver.observe(document.body, {
                childList: true,
                subtree: true
            });

            this.observers.set('editor', editorObserver);
            console.log('📝 Editor monitoring setup');
        }

        // Setup selection monitoring
        setupSelectionMonitoring() {
            // Monitor document selection changes
            document.addEventListener('selectionchange', () => {
                this.handleSelectionChange();
            });

            // Monitor click events for node selection
            document.addEventListener('click', (e) => {
                const node = e.target.closest(MONITOR_CONFIG.selectors.nodes);
                if (node) {
                    this.handleNodeClick(node, e);
                }
            });

            console.log('🎯 Selection monitoring setup');
        }

        // Setup performance monitoring
        setupPerformanceMonitoring() {
            setInterval(() => {
                this.updatePerformanceMetrics();
                this.checkPerformanceThresholds();
            }, MONITOR_CONFIG.intervals.performanceCheck);

            console.log('⚡ Performance monitoring setup');
        }

        // Event handlers
        handleNodeAdded(node) {
            const nodeId = this.getNodeId(node);
            console.log(`➕ Node added: ${nodeId}`);
            
            // Start monitoring the new node
            setTimeout(() => {
                this.monitorNode(node);
            }, 100);
            
            this.triggerEvent('nodeAdded', { node, nodeId, timestamp: Date.now() });
        }

        handleNodeRemoved(node) {
            const nodeId = this.getNodeId(node);
            console.log(`➖ Node removed: ${nodeId}`);
            
            // Clean up monitoring for removed node
            this.cleanupNodeMonitoring(nodeId);
            
            this.triggerEvent('nodeRemoved', { node, nodeId, timestamp: Date.now() });
        }

        handleNodeSelected(node, nodeData) {
            console.log(`🎯 Node selected: ${nodeData.id}`);
            
            this.monitoringState.selections.add(nodeData.id);
            this.triggerEvent('nodeSelected', { node, nodeData, timestamp: Date.now() });
        }

        handleNodeDeselected(node, nodeData) {
            console.log(`⭕ Node deselected: ${nodeData.id}`);
            
            this.monitoringState.selections.delete(nodeData.id);
            this.triggerEvent('nodeDeselected', { node, nodeData, timestamp: Date.now() });
        }

        handleEditorOpened(editor) {
            const editorId = this.getEditorId(editor);
            console.log(`📝 Editor opened: ${editorId}`);
            
            const editorData = {
                id: editorId,
                element: editor,
                nodeType: this.getEditorNodeType(editor),
                opened: Date.now()
            };
            
            this.monitoringState.editors.set(editorId, editorData);
            this.triggerEvent('editorOpened', { editor, editorData, timestamp: Date.now() });
        }

        handleEditorClosed(editor) {
            const editorId = this.getEditorId(editor);
            console.log(`📝 Editor closed: ${editorId}`);
            
            const editorData = this.monitoringState.editors.get(editorId);
            if (editorData) {
                editorData.closed = Date.now();
                editorData.duration = editorData.closed - editorData.opened;
            }
            
            this.monitoringState.editors.delete(editorId);
            this.triggerEvent('editorClosed', { editor, editorData, timestamp: Date.now() });
        }

        handleSelectionChange() {
            const selection = window.getSelection();
            if (selection.toString().trim()) {
                console.log('📋 Text selection changed');
            }
        }

        handleNodeClick(node, event) {
            const nodeId = this.getNodeId(node);
            const nodeData = this.monitoringState.nodes.get(nodeId);
            
            if (nodeData) {
                nodeData.lastClick = Date.now();
                nodeData.clickCount = (nodeData.clickCount || 0) + 1;
            }
        }

        // Update methods
        updateNodeState(nodeId, nodeData) {
            // Update node state and trigger any necessary actions
            this.monitoringState.nodes.set(nodeId, nodeData);
        }

        updatePerformanceMetrics() {
            const performance = this.monitoringState.performance;
            
            performance.nodeCount = this.monitoringState.nodes.size;
            performance.observerCount = this.observers.size;
            performance.lastUpdate = Date.now();
            
            // Estimate memory usage (rough calculation)
            performance.memoryUsage = (
                this.monitoringState.nodes.size * 1000 + // ~1KB per node
                this.observers.size * 500 + // ~500B per observer
                this.monitoringState.editors.size * 2000 // ~2KB per editor
            );
        }

        checkPerformanceThresholds() {
            const perf = this.monitoringState.performance;
            
            if (perf.nodeCount > MONITOR_CONFIG.thresholds.maxNodes) {
                this.triggerEvent('performanceAlert', {
                    type: 'nodeCount',
                    value: perf.nodeCount,
                    threshold: MONITOR_CONFIG.thresholds.maxNodes
                });
            }
            
            if (perf.observerCount > MONITOR_CONFIG.thresholds.maxObservers) {
                this.triggerEvent('performanceAlert', {
                    type: 'observerCount',
                    value: perf.observerCount,
                    threshold: MONITOR_CONFIG.thresholds.maxObservers
                });
            }
            
            if (perf.memoryUsage > MONITOR_CONFIG.thresholds.memoryLimit) {
                this.triggerEvent('performanceAlert', {
                    type: 'memoryUsage',
                    value: perf.memoryUsage,
                    threshold: MONITOR_CONFIG.thresholds.memoryLimit
                });
            }
        }

        // Utility methods
        isNodeElement(element) {
            return element.nodeType === 1 && 
                   element.matches && 
                   element.matches(MONITOR_CONFIG.selectors.nodes);
        }

        isEditorElement(element) {
            return element.nodeType === 1 && 
                   element.matches && 
                   element.matches(MONITOR_CONFIG.selectors.editor);
        }

        getNodeId(node) {
            return node.getAttribute('data-id') || 
                   node.id || 
                   `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        }

        getNodeType(node) {
            const typeElement = node.querySelector('[data-node-type]');
            return typeElement ? typeElement.getAttribute('data-node-type') : 'unknown';
        }

        getEditorId(editor) {
            return editor.id || 
                   editor.getAttribute('data-editor-id') ||
                   `editor-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        }

        getEditorNodeType(editor) {
            // Try to determine the node type from the editor context
            const nodeTypeElement = editor.querySelector('[data-node-type]');
            return nodeTypeElement ? nodeTypeElement.getAttribute('data-node-type') : 'unknown';
        }

        // Event system
        addEventListener(eventType, handler) {
            if (!this.eventHandlers.has(eventType)) {
                this.eventHandlers.set(eventType, []);
            }
            this.eventHandlers.get(eventType).push(handler);
        }

        removeEventListener(eventType, handler) {
            if (this.eventHandlers.has(eventType)) {
                const handlers = this.eventHandlers.get(eventType);
                const index = handlers.indexOf(handler);
                if (index > -1) {
                    handlers.splice(index, 1);
                }
            }
        }

        triggerEvent(eventType, data) {
            if (this.eventHandlers.has(eventType)) {
                this.eventHandlers.get(eventType).forEach(handler => {
                    try {
                        handler(data);
                    } catch (error) {
                        console.error(`Error in event handler for ${eventType}:`, error);
                    }
                });
            }
        }

        // Cleanup methods
        cleanupNodeMonitoring(nodeId) {
            this.monitoringState.nodes.delete(nodeId);
            
            const nodeObserver = this.observers.get(`node-${nodeId}`);
            if (nodeObserver) {
                nodeObserver.disconnect();
                this.observers.delete(`node-${nodeId}`);
            }
            
            const contentObserver = this.observers.get(`node-content-${nodeId}`);
            if (contentObserver) {
                contentObserver.disconnect();
                this.observers.delete(`node-content-${nodeId}`);
            }
        }

        // Status and statistics
        getMonitoringStatus() {
            return {
                isMonitoring: this.isMonitoring,
                nodeCount: this.monitoringState.nodes.size,
                editorCount: this.monitoringState.editors.size,
                selectionCount: this.monitoringState.selections.size,
                observerCount: this.observers.size,
                performance: this.monitoringState.performance
            };
        }

        getDetailedStats() {
            const stats = this.getMonitoringStatus();
            
            stats.nodeDetails = Array.from(this.monitoringState.nodes.values()).map(node => ({
                id: node.id,
                type: node.type,
                selected: node.selected,
                changeCount: node.changeCount,
                lastUpdate: node.lastUpdate
            }));
            
            stats.editorDetails = Array.from(this.monitoringState.editors.values()).map(editor => ({
                id: editor.id,
                nodeType: editor.nodeType,
                opened: editor.opened,
                duration: editor.duration
            }));
            
            return stats;
        }

        // Cleanup
        cleanup() {
            this.stopMonitoring();
            this.eventHandlers.clear();
            this.monitoringState.nodes.clear();
            this.monitoringState.editors.clear();
            this.monitoringState.selections.clear();
            console.log('🧹 Real-time Monitor cleaned up');
        }
    }

    // Create global instance
    window.RealtimeMonitor = new RealtimeMonitor();

    // Integrate with Universal Node Modifier
    if (window.UniversalNodeModifier) {
        window.UniversalNodeModifier.monitor = window.RealtimeMonitor;
        
        // Add monitoring methods to the main modifier
        window.UniversalNodeModifier.startMonitoring = function() {
            return this.monitor.startMonitoring();
        };
        
        window.UniversalNodeModifier.stopMonitoring = function() {
            return this.monitor.stopMonitoring();
        };
        
        window.UniversalNodeModifier.getMonitoringStatus = function() {
            return this.monitor.getMonitoringStatus();
        };
        
        // Auto-start monitoring when Universal Node Modifier initializes
        const originalInitialize = window.UniversalNodeModifier.initialize;
        window.UniversalNodeModifier.initialize = function() {
            const result = originalInitialize.call(this);
            this.startMonitoring();
            return result;
        };
        
        console.log('🔍 Real-time Monitoring System integrated');
    }

    console.log('📦 Real-time Monitoring System loaded');

})();
