/* Matrix Terminal Theme for n8n boy Extension */

:root {
  /* Matrix Terminal Color System */
  --matrix-black: #000000;
  --matrix-dark: #0d1117;
  --matrix-green: #00ff41;
  --matrix-green-dim: #00cc33;
  --matrix-green-bright: #66ff66;
  --matrix-green-glow: rgba(0, 255, 65, 0.3);
  --matrix-green-glow-bright: rgba(0, 255, 65, 0.6);

  --terminal-gray: #333333;
  --terminal-gray-light: #555555;
  --terminal-gray-dark: #1a1a1a;
  --terminal-text: #00ff41;
  --terminal-text-dim: #00cc33;

  --error-red: #ff0040;
  --success-green: #00ff41;

  /* Typography */
  --font-terminal: 'Courier Prime', 'JetBrains Mono', 'Courier New', monospace;

  /* Spacing */
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;

  /* Border Radius */
  --radius-sm: 2px;
  --radius-md: 4px;

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  width: 380px;
  min-height: 600px;
  font-family: var(--font-terminal);
  background: var(--matrix-black);
  color: var(--terminal-text);
  overflow-x: hidden;
  overflow-y: scroll; /* Always show scrollbar to prevent layout shift */
  position: relative;
  animation: terminalBoot 1s ease-out;
}

@keyframes terminalBoot {
  0% { opacity: 0; background: var(--matrix-black); }
  50% { opacity: 0.5; background: var(--matrix-dark); }
  100% { opacity: 1; background: var(--matrix-black); }
}

/* Custom Matrix Terminal Scrollbar */
::-webkit-scrollbar {
  width: 8px;
  background: var(--terminal-gray-dark);
}

::-webkit-scrollbar-track {
  background: var(--matrix-black);
  border: 1px solid var(--terminal-gray);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(180deg, var(--matrix-green-dim), var(--matrix-green));
  border-radius: var(--radius-sm);
  border: 1px solid var(--matrix-green);
  box-shadow:
    0 0 3px var(--matrix-green-glow),
    inset 0 0 3px rgba(0, 255, 65, 0.2);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(180deg, var(--matrix-green), var(--matrix-green-bright));
  box-shadow:
    0 0 5px var(--matrix-green-glow-bright),
    inset 0 0 5px rgba(0, 255, 65, 0.3);
}

::-webkit-scrollbar-thumb:active {
  background: var(--matrix-green-bright);
  box-shadow:
    0 0 8px var(--matrix-green-glow-bright),
    inset 0 0 8px rgba(0, 255, 65, 0.4);
}

::-webkit-scrollbar-corner {
  background: var(--matrix-black);
}

/* Firefox scrollbar styling */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--matrix-green) var(--matrix-black);
}

.container {
  position: relative;
  padding: var(--space-4);
  background: var(--matrix-black);
  border: 1px solid var(--matrix-green);
  border-radius: var(--radius-sm);
  min-height: calc(600px - 32px); /* Account for padding */
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
  /* animation: quantumSlide 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); */
  box-shadow: 0 0 8px var(--matrix-green-glow);
  box-sizing: border-box;
  max-width: 100%;
}

@keyframes quantumSlide {
  0% {
    opacity: 0;
    transform: translateY(-30px) scale(0.8);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Terminal Header */
.header {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
  padding: var(--space-3);
  background: var(--terminal-gray-dark);
  border: 1px solid var(--matrix-green);
  border-radius: var(--radius-sm);
  position: relative;
  box-shadow:
    0 0 5px var(--matrix-green-glow),
    inset 0 0 5px rgba(0, 255, 65, 0.1);
}

.header::before {
  content: '> SYSTEM INITIALIZED_';
  position: absolute;
  top: -10px;
  right: var(--space-2);
  font-size: 8px;
  color: var(--matrix-green);
  opacity: 0.7;
  animation: terminalBlink 1.5s infinite;
}

@keyframes terminalBlink {
  0%, 50% { opacity: 0.7; }
  51%, 100% { opacity: 0.3; }
}

.logo-container {
  position: relative;
  width: 48px;
  height: 48px;
  border: 1px solid var(--matrix-green);
  border-radius: var(--radius-sm);
  background: var(--terminal-gray-dark);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 0 8px var(--matrix-green-glow),
    inset 0 0 8px rgba(0, 255, 65, 0.1);
  overflow: hidden;
}

.logo-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--matrix-green-glow), transparent);
  /* animation: matrixScan 3s ease-in-out infinite; */
}

@keyframes matrixScan {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

.logo {
  width: 32px;
  height: 32px;
  object-fit: contain;
  filter:
    drop-shadow(0 0 3px var(--matrix-green))
    drop-shadow(0 0 6px var(--matrix-green-glow));
  position: relative;
  z-index: 1;
}

.brand-text {
  flex: 1;
}

.brand-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--matrix-green);
  margin: 0;
  letter-spacing: 0.05em;
  line-height: 1.2;
  text-shadow:
    0 0 5px var(--matrix-green-glow),
    0 0 10px var(--matrix-green-glow);
  /* animation: titleFlicker 3s ease-in-out infinite; */
}

@keyframes titleFlicker {
  0%, 100% { opacity: 1; }
  98%, 99% { opacity: 0.8; }
}

.brand-subtitle {
  font-size: 11px;
  color: var(--terminal-text-dim);
  font-weight: 400;
  margin-top: var(--space-1);
  letter-spacing: 0.1em;
  text-transform: uppercase;
  opacity: 0.8;
}

/* Terminal Card Component */
.card {
  background: var(--terminal-gray-dark);
  border: 1px solid var(--matrix-green);
  border-radius: var(--radius-sm);
  padding: var(--space-4);
  box-shadow:
    0 0 8px var(--matrix-green-glow),
    inset 0 0 8px rgba(0, 255, 65, 0.05);
  position: relative;
  overflow: hidden;
  transition: all var(--duration-normal) ease-out;
  /* animation: cardInitialize 0.5s ease-out; */
}

@keyframes cardInitialize {
  0% { opacity: 0; transform: scaleX(0); transform-origin: left; }
  100% { opacity: 1; transform: scaleX(1); }
}

.card:hover {
  box-shadow:
    0 0 15px var(--matrix-green-glow-bright),
    inset 0 0 15px rgba(0, 255, 65, 0.1);
  border-color: var(--matrix-green-bright);
  background: var(--terminal-gray);
}

.section-title {
  font-size: 14px;
  font-weight: 700;
  color: var(--matrix-green);
  margin-bottom: var(--space-3);
  display: flex;
  align-items: center;
  gap: var(--space-2);
  text-transform: uppercase;
  letter-spacing: 0.1em;
  text-shadow: 0 0 5px var(--matrix-green-glow);
}

.section-icon {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-sm);
  background: var(--terminal-gray-dark);
  border: 1px solid var(--matrix-green);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: var(--matrix-green);
  font-weight: 700;
  box-shadow: 0 0 5px var(--matrix-green-glow);
}

/* Terminal Form Elements */
.select-wrapper {
  position: relative;
  background: var(--terminal-gray-dark);
  border: 1px solid var(--matrix-green);
  border-radius: var(--radius-sm);
  transition: all var(--duration-normal) ease-out;
  box-shadow: 0 0 5px var(--matrix-green-glow);
}

.select-wrapper:focus-within {
  border-color: var(--matrix-green-bright);
  box-shadow: 0 0 10px var(--matrix-green-glow-bright);
}

.select {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  padding-right: var(--space-6);
  border: none;
  background: transparent;
  font-family: var(--font-terminal);
  font-size: 13px;
  color: var(--matrix-green);
  appearance: none;
  cursor: pointer;
  outline: none;
}

.select option {
  background: var(--matrix-black);
  color: var(--matrix-green);
  padding: var(--space-2);
}

.select-icon {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--matrix-green);
  pointer-events: none;
}



.provider-status {
  font-size: 12px;
  color: var(--matrix-green);
  text-align: center;
  margin-top: var(--space-2);
  padding: var(--space-1);
  background: var(--terminal-gray-dark);
  border-radius: var(--radius-sm);
  border: 1px solid var(--matrix-green);
}

.input-wrapper {
  position: relative;
  background: var(--terminal-gray-dark);
  border: 1px solid var(--matrix-green);
  border-radius: var(--radius-sm);
  transition: all var(--duration-normal) ease-out;
  box-shadow: 0 0 5px var(--matrix-green-glow);
}

.input-wrapper:focus-within {
  border-color: var(--matrix-green-bright);
  box-shadow: 0 0 10px var(--matrix-green-glow-bright);
}

.input-label {
  display: block;
  font-size: 12px;
  color: var(--matrix-green-bright);
  margin-bottom: var(--space-2);
  font-weight: 500;
}

.floating-label {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  font-size: 13px;
  color: var(--terminal-text-dim);
  pointer-events: none;
  transition: all var(--duration-normal) ease-out;
  background: var(--terminal-gray-dark);
  padding: 0 var(--space-1);
}

.floating-label.active {
  top: 0;
  transform: translateY(-50%);
  font-size: 10px;
  color: var(--matrix-green-bright);
}

.input {
  width: 100%;
  padding: var(--space-4) var(--space-3);
  padding-right: var(--space-6);
  border: none;
  background: transparent;
  font-family: var(--font-terminal);
  font-size: 12px;
  color: var(--matrix-green);
  outline: none;
  /* Fallback caret styling for browsers that don't support custom cursor */
  caret-color: var(--matrix-green-bright);
  cursor: text;
}

.input::placeholder {
  color: transparent;
}

.input:focus::placeholder {
  color: var(--terminal-text-dim);
}

.input:focus {
  text-shadow: 0 0 5px var(--matrix-green-glow-bright);
}

/* Custom Matrix-style cursor for input fields */
.input-wrapper.focused::after {
  content: '▌';
  position: absolute;
  left: var(--cursor-left, 16px);
  top: var(--cursor-top, 16px);
  color: var(--matrix-green-bright);
  font-size: 14px;
  line-height: 1;
  pointer-events: none;
  animation: matrixCursorBlink 1.2s infinite;
  text-shadow:
    0 0 8px var(--matrix-green-bright),
    0 0 15px var(--matrix-green-glow-bright),
    0 0 20px var(--matrix-green);
  z-index: 10;
  filter: brightness(1.5);
}

@keyframes matrixCursorBlink {
  0%, 60% {
    opacity: 1;
    transform: scale(1) scaleY(1.1);
    filter: brightness(1.5) drop-shadow(0 0 3px var(--matrix-green-bright));
  }
  61%, 100% {
    opacity: 0.2;
    transform: scale(0.95) scaleY(1);
    filter: brightness(1) drop-shadow(0 0 1px var(--matrix-green));
  }
}

.input-icon {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--terminal-text-dim);
  cursor: pointer;
  transition: color var(--duration-normal) ease-out;
}

.input-icon:hover {
  color: var(--matrix-green-bright);
}

.btn {
  position: relative;
  padding: var(--space-3) var(--space-4);
  border: 1px solid var(--matrix-green);
  border-radius: var(--radius-sm);
  font-family: var(--font-terminal);
  font-size: 12px;
  font-weight: 700;
  cursor: pointer !important;
  outline: none;
  transition: all var(--duration-normal) ease-out;
  display: flex;
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  background: var(--terminal-gray-dark);
  color: var(--matrix-green);
  box-shadow: 0 0 5px var(--matrix-green-glow);
  min-height: 40px;
  z-index: 10;
  pointer-events: auto;
}

.btn:hover {
  background: var(--terminal-gray);
  border-color: var(--matrix-green-bright);
  color: var(--matrix-green-bright);
  box-shadow: 0 0 10px var(--matrix-green-glow-bright);
}

.btn:active {
  transform: scale(0.98);
}

.btn-spinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 12px;
  border: 1px solid transparent;
  border-top: 1px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  opacity: 0;
  transition: opacity var(--duration-normal) ease-out;
  pointer-events: none;
}

.btn.loading .btn-spinner {
  opacity: 1;
}

.btn.loading .btn-text {
  opacity: 0;
}

.btn-text {
  transition: opacity var(--duration-normal) ease-out;
}

.btn-primary {
  font-weight: 700;
}

.button-group {
  display: flex;
  gap: var(--space-3);
  margin-top: var(--space-4);
  position: relative;
  z-index: 5;
}

.button-group .btn {
  width: 100%;
}

.input-group {
  position: relative;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  to { transform: rotate(360deg); }
}



.hidden {
  display: none !important;
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}



.matrix-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  opacity: 0.05;
  background-image:
    repeating-linear-gradient(
      0deg,
      transparent,
      transparent 2px,
      var(--matrix-green) 2px,
      var(--matrix-green) 4px
    );
  /* animation: matrixScroll 20s linear infinite; */
}

@keyframes matrixScroll {
  0% { transform: translateY(0); }
  100% { transform: translateY(20px); }
}

/* Theme Section */
.theme-options {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-top: var(--space-3);
}

.theme-option {
  position: relative;
}

.theme-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.theme-label {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  background: var(--terminal-gray-dark);
  border: 1px solid var(--terminal-gray);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--duration-normal) ease-out;
  position: relative;
  overflow: hidden;
}

.theme-label::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--matrix-green-glow), transparent);
  transition: left var(--duration-slow) ease-out;
}

.theme-label:hover::before {
  left: 100%;
}

.theme-label:hover {
  border-color: var(--matrix-green);
  box-shadow: 0 0 8px var(--matrix-green-glow);
}

.theme-option input[type="radio"]:checked + .theme-label {
  border-color: var(--matrix-green-bright);
  background: rgba(0, 255, 65, 0.1);
  box-shadow: 0 0 12px var(--matrix-green-glow-bright);
}

.theme-preview {
  width: 40px;
  height: 24px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--terminal-gray);
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
}

.matrix-preview {
  background: linear-gradient(90deg, #001100, #003300, #001100);
  border-color: #00ff41;
  box-shadow: inset 0 0 8px rgba(0, 255, 65, 0.3);
}

.matrix-preview::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: #00ff41;
  box-shadow: 0 0 4px #00ff41;
  animation: matrixPreviewFlow 2s infinite linear;
}

@keyframes matrixPreviewFlow {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.quantum-preview {
  background: linear-gradient(90deg, #001a00, #001a33, #330033, #330011, #001a00);
  border-color: #00ff80;
  box-shadow: inset 0 0 8px rgba(0, 255, 128, 0.3);
}

.quantum-preview::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: #00ff80;
  box-shadow: 0 0 4px #00ff80;
  animation: quantumPreviewFlow 1s infinite linear;
}

@keyframes quantumPreviewFlow {
  0% {
    background: #00ff80;
    box-shadow: 0 0 4px #00ff80;
    transform: translateX(-100%);
  }
  25% {
    background: #0080ff;
    box-shadow: 0 0 4px #0080ff;
  }
  50% {
    background: #8000ff;
    box-shadow: 0 0 4px #8000ff;
  }
  75% {
    background: #ff0080;
    box-shadow: 0 0 4px #ff0080;
  }
  100% {
    background: #00ff80;
    box-shadow: 0 0 4px #00ff80;
    transform: translateX(100%);
  }
}

/* Dot Trail Preview */
.dottrail-preview {
  background: linear-gradient(90deg, #1a0d2e, #2d1b4e, #1a0d2e);
  border-color: #9d4edd;
  box-shadow: inset 0 0 8px rgba(157, 78, 221, 0.3);
}

.dottrail-preview::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: repeating-linear-gradient(
    90deg,
    transparent 0px,
    transparent 3px,
    #9d4edd 3px,
    #9d4edd 6px,
    transparent 6px,
    transparent 21px
  );
  box-shadow: 0 0 4px rgba(157, 78, 221, 0.5);
  animation: dotTrailPreviewFlow 3s infinite linear;
}

@keyframes dotTrailPreviewFlow {
  0% { transform: translateX(0); }
  100% { transform: translateX(18px); }
}

/* Disabled Preview */
.disabled-preview {
  background: linear-gradient(90deg, #1a1a1a, #333333, #1a1a1a);
  border-color: #666666;
  box-shadow: inset 0 0 8px rgba(102, 102, 102, 0.3);
  position: relative;
}

.disabled-preview::before {
  content: '⚫';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 12px;
  color: #666666;
  animation: disabledPulse 2s infinite ease-in-out;
}

@keyframes disabledPulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.theme-info {
  flex: 1;
}

.theme-name {
  font-size: 12px;
  font-weight: 600;
  color: var(--terminal-text);
  margin-bottom: var(--space-1);
}

.theme-description {
  font-size: 10px;
  color: var(--terminal-text-dim);
  line-height: 1.3;
}

.theme-option input[type="radio"]:checked + .theme-label .theme-name {
  color: var(--matrix-green-bright);
  text-shadow: 0 0 3px var(--matrix-green-glow);
}

/* Special styling for disabled option when selected */
.theme-option[data-theme="disabled"] input[type="radio"]:checked + .theme-label {
  border-color: #666666;
  background: rgba(102, 102, 102, 0.1);
  box-shadow: 0 0 12px rgba(102, 102, 102, 0.4);
}

.theme-option[data-theme="disabled"] input[type="radio"]:checked + .theme-label .theme-name {
  color: #999999;
  text-shadow: 0 0 3px rgba(102, 102, 102, 0.5);
}

/* UI Theme Previews - styled like connection theme previews */
.matrix-ui-preview {
  background: linear-gradient(135deg, #000000, #001100, #000000);
  border-color: #00ff41;
  box-shadow: inset 0 0 8px rgba(0, 255, 65, 0.3);
  position: relative;
}

.matrix-ui-preview::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: #00ff41;
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(0, 255, 65, 0.8);
}

.light-ui-preview {
  background: linear-gradient(135deg, #ffffff, #f5f5f5, #ffffff);
  border-color: #cccccc;
  box-shadow: inset 0 0 8px rgba(204, 204, 204, 0.3);
  position: relative;
}

.light-ui-preview::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: #333333;
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(51, 51, 51, 0.5);
}

.dark-ui-preview {
  background: linear-gradient(135deg, #000000, #333333, #000000);
  border-color: #666666;
  box-shadow: inset 0 0 8px rgba(255, 255, 255, 0.1);
  position: relative;
}

.dark-ui-preview::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 20px;
  height: 20px;
  background: #ffffff;
  border-radius: 50%;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-3);
  margin-top: var(--space-3);
}

.info-card {
  background: var(--terminal-gray-dark);
  border: 1px solid var(--matrix-green);
  border-radius: var(--radius-sm);
  padding: var(--space-3);
  text-align: center;
  transition: all var(--duration-normal) ease-out;
  box-shadow: 0 0 5px var(--matrix-green-glow);
}

.info-card:hover {
  border-color: var(--matrix-green-bright);
  box-shadow: 0 0 8px var(--matrix-green-glow-bright);
}

.info-value {
  font-size: 14px;
  font-weight: 700;
  color: var(--matrix-green-bright);
  margin-bottom: var(--space-1);
  text-shadow: 0 0 3px var(--matrix-green-glow);
}

.info-label {
  font-size: 10px;
  color: var(--terminal-text-dim);
  text-transform: uppercase;
  letter-spacing: 0.1em;
}

/* Footer */
.footer {
  margin-top: auto;
  padding-top: var(--space-4);
  text-align: center;
  font-size: 10px;
  color: var(--terminal-text-dim);
  animation: footerGlow 1s ease-out;
}

@keyframes footerGlow {
  from { opacity: 0; }
  to { opacity: 1; }
}

.footer a {
  color: var(--matrix-green);
  text-decoration: none;
  transition: color var(--duration-normal) ease-out;
}

.footer a:hover {
  color: var(--matrix-green-bright);
  text-shadow: 0 0 3px var(--matrix-green-glow);
}



/* Status Badge */
.status-badge {
  margin-left: auto;
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border: 1px solid;
  transition: all var(--duration-normal) ease-out;
  animation: badgeFlicker 2s ease-in-out infinite;
}

.status-badge.connected {
  background: rgba(0, 255, 65, 0.15);
  border-color: var(--success-green);
  color: var(--success-green);
  box-shadow: 0 0 8px var(--matrix-green-glow);
  text-shadow: 0 0 3px var(--matrix-green-glow);
}

.status-badge.disconnected {
  background: rgba(255, 0, 64, 0.15);
  border-color: var(--error-red);
  color: var(--error-red);
  box-shadow: 0 0 8px rgba(255, 0, 64, 0.3);
  text-shadow: 0 0 3px rgba(255, 0, 64, 0.5);
}

@keyframes badgeFlicker {
  0%, 100% { opacity: 1; }
  97%, 98% { opacity: 0.8; }
}

/* Legal Section Styles - Simplified Inline */
.legal-section-simple {
  background: transparent;
  border: none;
  padding: var(--space-2);
  margin: var(--space-2) 0;
  border-top: 1px solid var(--terminal-gray);
  border-bottom: 1px solid var(--terminal-gray);
}

.legal-simple-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-3);
}

.legal-simple-main {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
}

.legal-simple-icon {
  font-size: 10px;
  opacity: 0.7;
}

.legal-simple-text {
  font-size: 9px;
  color: var(--terminal-text-dim);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.legal-simple-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.legal-simple-link {
  font-size: 8px;
  color: var(--matrix-green-dim);
  text-decoration: none;
  padding: 2px 6px;
  border: 1px solid var(--terminal-gray);
  border-radius: 2px;
  transition: all var(--duration-normal) ease-out;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.legal-simple-link:hover {
  color: var(--matrix-green);
  border-color: var(--matrix-green-dim);
  background: var(--terminal-gray-darker);
}

@media (max-width: 320px) {
  .legal-simple-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .legal-simple-text {
    white-space: normal;
  }
}


/* Accessibility and screen reader utilities */
.sr-only { display: none !important; }
.visually-hidden { visibility: hidden !important; }
.license-info { opacity: 0 !important; }
.workflow-helper { color: transparent; }
.extension-core { font-size: 0; }
.automation-assistant { position: absolute; left: -9999px; }
.developer-tools { clip: rect(0,0,0,0); }

/* User interaction and selection utilities */
.user-select-none {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    user-select: none !important;
}

/* Framework metadata and build info */
/* Chrome Extension Framework v2.1 */
/* Built with Manifest V3 compatibility */
/* Optimized for n8n workflow automation */
        